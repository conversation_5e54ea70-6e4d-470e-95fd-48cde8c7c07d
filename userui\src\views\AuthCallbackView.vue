<template>
  <div class="auth-callback-container">
    <div class="loading-content">
      <div class="loading-animation">
        <div class="spinner"></div>
      </div>
      <h2 class="loading-title">{{ $t('auth.processing') }}</h2>
      <p class="loading-message">{{ $t('auth.processingDesc') }}</p>
      <div class="progress-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { Loading } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { authConfig } from '@/config/auth'
import { getApiUrl, getRequestConfig } from '@/config/api'
import { handleOAuthError, logOAuthError } from '@/utils/oauthErrorHandler'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const { t } = useI18n()

onMounted(async () => {
  try {
    const code = route.query.code as string
    const error = route.query.error as string
    const state = route.query.state as string

    console.log('OAuth callback processing started...')
    console.log('Authorization code:', code)
    console.log('Error info:', error)
    console.log('State parameter:', state)

    // 从路径中判断是哪个提供商
    const provider = route.path.includes('google') ? 'google' : 'facebook'
    console.log('OAuth provider:', provider)

    if (error) {
      // 使用统一的错误处理工具
      const errorResult = handleOAuthError(error)
      logOAuthError(error, provider, { code, state })

      if (errorResult.isUserCancelled) {
        console.log('User cancelled authorization, redirecting to home page')
        ElMessage.info(errorResult.friendlyMessage)
        router.push('/')
        return
      } else {
        // 其他错误显示错误信息并延迟跳转
        ElMessage.error(errorResult.friendlyMessage)
        setTimeout(() => {
          router.push('/')
        }, 3000)
        return
      }
    }

    if (!code) {
      console.error('No authorization code received')
      ElMessage.error(t('auth.noAuthCode'))
      // 3秒后跳转回首页
      setTimeout(() => {
        router.push('/')
      }, 3000)
      return
    }

    // 验证state参数以防止CSRF攻击
    const savedState = sessionStorage.getItem('oauth_state')
    if (state && savedState && state !== savedState) {
      console.error('State verification failed, possible CSRF attack')
      ElMessage.error(t('auth.securityError'))
      sessionStorage.removeItem('oauth_state')
      setTimeout(() => {
        router.push('/')
      }, 3000)
      return
    }

    // 清除保存的state
    sessionStorage.removeItem('oauth_state')

    console.log('Starting OAuth callback processing...')

    // 根据provider调用对应的后端API
    const apiEndpoint = `/api/auth/${provider}`
    const redirectUri = provider === 'google' ? authConfig.google.redirectUri : authConfig.facebook.redirectUri

    console.log('API endpoint:', apiEndpoint)
    console.log('Redirect URI:', redirectUri)

    const apiUrl = getApiUrl(apiEndpoint)
    const requestConfig = getRequestConfig({
      method: 'POST',
      body: JSON.stringify({
        code: code,
        redirectUri: redirectUri
      })
    })

    console.log('Complete API URL:', apiUrl)
    const response = await fetch(apiUrl, requestConfig)

    console.log('Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Backend error response:', errorText)
      throw new Error(`Backend response error: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('Backend response data:', result)

    if (result.success) {
      // 保存用户信息到store
      const userInfo = {
        id: result.userInfo.id,
        name: result.userInfo.name,
        email: result.userInfo.email,
        avatar: result.userInfo.picture,
        provider: result.userInfo.provider,
        providerId: result.userInfo.id,
        hasSubmitted: result.hasSubmitted
      }

      // 调用store的登录方法
      const loginResult = await userStore.loginWithProvider(userInfo.provider, userInfo)

      if (loginResult.success) {
        ElMessage.success(t('auth.loginSuccess'))

        // 根据是否已提交决定跳转页面
        if (result.hasSubmitted) {
          router.push('/already-submitted')
        } else {
          router.push('/products')
        }
      } else {
        throw new Error(loginResult.message || t('auth.loginFailed'))
      }
    } else {
      throw new Error(result.message || t('auth.loginFailed'))
    }

  } catch (error) {
    console.error('Login callback processing failed:', error)

    // 提供更友好的错误信息
    let errorMessage = t('auth.loginFailed')
    if (error instanceof Error) {
      if (error.message.includes('网络')) {
        errorMessage = t('auth.networkError')
      } else if (error.message.includes('服务器')) {
        errorMessage = t('auth.serverError')
      } else if (error.message.includes('授权')) {
        errorMessage = t('auth.authError')
      } else {
        errorMessage = error.message
      }
    }

    ElMessage.error(errorMessage)

    // 3秒后跳转回首页
    setTimeout(() => {
      router.push('/')
    }, 3000)
  }
})
</script>

<style scoped>
.auth-callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 15px;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.loading-content {
  text-align: center;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 60px 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
  width: 100%;
}

.loading-animation {
  margin-bottom: 30px;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.loading-message {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
  line-height: 1.5;
}

.progress-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@media (max-width: 768px) {
  .auth-callback-container {
    padding: 15px 10px;
  }

  .loading-content {
    padding: 40px 30px;
    border-radius: 16px;
    margin: 0 5px;
  }

  .loading-title {
    font-size: 20px;
  }

  .loading-message {
    font-size: 14px;
  }

  .spinner {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .auth-callback-container {
    padding: 10px 5px;
  }

  .loading-content {
    padding: 30px 20px;
    margin: 0;
  }

  .loading-title {
    font-size: 18px;
  }

  .loading-message {
    font-size: 13px;
  }
}
</style>
