import { defineStore } from 'pinia'
import { ref } from 'vue'

// 用户信息接口
export interface UserInfo {
  id: number
  userName: string
  userSex: string
  country: string
  birthday?: Date
  address: string
}

/**
 * 用户状态管理Store
 * 用于处理普通用户的状态管理
 */
export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<UserInfo | null>(null)
  const loading = ref(false)

  /**
   * 设置当前用户信息
   * @param user 用户信息
   */
  const setCurrentUser = (user: UserInfo) => {
    currentUser.value = user
    localStorage.setItem('currentUser', JSON.stringify(user))
  }

  /**
   * 清除当前用户信息
   */
  const clearCurrentUser = () => {
    currentUser.value = null
    localStorage.removeItem('currentUser')
  }

  /**
   * 初始化用户状态
   * 从localStorage恢复用户信息
   */
  const initUserState = () => {
    const savedUser = localStorage.getItem('currentUser')

    if (savedUser) {
      try {
        currentUser.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('恢复用户状态失败:', error)
        clearCurrentUser()
      }
    }
  }

  return {
    // 状态
    currentUser,
    loading,

    // 方法
    setCurrentUser,
    clearCurrentUser,
    initUserState
  }
})
