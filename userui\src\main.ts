import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import axios from 'axios'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import i18n from './locales'
import { apiConfig } from '@/config/api'

// 配置axios默认设置 - 自动适配协议
const getApiBaseUrl = () => {
  if (import.meta.env.DEV) {
    return '' // 开发环境使用代理
  }

  // 生产环境：自动适配当前页面协议
  const currentProtocol = window.location.protocol.replace(':', '')
  const host = import.meta.env.VITE_API_HOST || 'localhost'

  // 根据当前页面协议选择对应的后端端口
  const port = currentProtocol === 'https' ? 8081 : 8080
  const baseUrl = `${currentProtocol}://${host}:${port}`

  console.log(`🚀 UserUI API配置: ${baseUrl}`)
  return baseUrl
}

axios.defaults.baseURL = getApiBaseUrl()
axios.defaults.timeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000')

// 响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(ElementPlus)

// 将axios挂载到全局属性
app.config.globalProperties.$axios = axios

// 直接挂载应用，让路由守卫处理初始化
app.mount('#app')
