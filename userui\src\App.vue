<script setup lang="ts">
import { RouterView } from 'vue-router'

// App.vue 不再处理状态轮询，避免与路由守卫冲突
// 状态检查完全由路由守卫和页面组件自己处理
console.log('App.vue 已加载，状态检查由路由守卫处理')
</script>

<template>
  <div id="app">
    <!-- 主要内容区域 -->
    <div class="app-main">
      <RouterView />
    </div>
  </div>
</template>

<style scoped>
#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.app-main {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  position: relative;
}

h1 {
  color: #333;
  font-size: 24px;
}

p {
  color: #666;
  font-size: 16px;
}
</style>
