<template>
  <div class="home-container">
    <div class="loading-content">
      <div class="loading-animation">
        <div class="spinner"></div>
      </div>
      <h2 class="loading-title">正在加载...</h2>
      <p class="loading-message">请稍候，我们正在为您准备页面...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

onMounted(async () => {
  console.log('HomeView: 开始智能重定向')
  
  // 等待用户状态初始化完成
  let attempts = 0
  const maxAttempts = 50 // 最多等待5秒
  
  while (!userStore.isInitialized && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100))
    attempts++
  }
  
  console.log('HomeView: 用户状态初始化完成')
  console.log('HomeView: 用户登录状态:', userStore.isLoggedIn)
  console.log('HomeView: 用户提交状态:', userStore.hasSubmitted)
  
  // 根据用户状态进行重定向
  if (userStore.isLoggedIn) {
    if (userStore.hasSubmitted) {
      console.log('HomeView: 用户已提交，跳转到已提交页面')
      router.replace('/already-submitted')
    } else {
      console.log('HomeView: 用户已登录但未提交，跳转到产品页面')
      router.replace('/products')
    }
  } else {
    console.log('HomeView: 用户未登录，跳转到登录页面')
    router.replace('/login')
  }
})
</script>

<style scoped>
.home-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 15px;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.loading-content {
  text-align: center;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 60px 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 400px;
  width: 100%;
}

.loading-animation {
  margin-bottom: 30px;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.loading-message {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
  line-height: 1.5;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .home-container {
    padding: 15px 10px;
  }

  .loading-content {
    padding: 40px 30px;
    border-radius: 16px;
    margin: 0 5px;
  }

  .loading-title {
    font-size: 20px;
  }

  .loading-message {
    font-size: 14px;
  }

  .spinner {
    width: 50px;
    height: 50px;
  }
}
</style>
