/* RTL (Right-to-Left) 样式支持 */

/* 阿拉伯语RTL布局 */
[dir="rtl"] {
  text-align: right;
}

/* 表单标签对齐 */
[dir="rtl"] .el-form-item__label {
  text-align: right;
}

/* 按钮组RTL */
[dir="rtl"] .login-buttons {
  direction: rtl;
}

[dir="rtl"] .actions {
  direction: rtl;
}

/* 导航按钮RTL */
[dir="rtl"] .navigation-section {
  direction: rtl;
  justify-content: flex-start;
}

[dir="rtl"] .nav-button {
  direction: ltr; /* 按钮内容保持LTR */
}

/* 产品网格RTL */
[dir="rtl"] .product-grid {
  direction: rtl;
}

[dir="rtl"] .product-card {
  direction: ltr; /* 产品卡片内容保持LTR */
}

/* 用户信息RTL */
[dir="rtl"] .user-header {
  direction: rtl;
}

[dir="rtl"] .user-info {
  direction: rtl;
}

/* 选择信息RTL */
[dir="rtl"] .selection-info {
  direction: rtl;
}

[dir="rtl"] .selection-hint {
  direction: rtl;
}

/* 图标在RTL中的位置调整 */
[dir="rtl"] .el-icon--right {
  margin-left: 0;
  margin-right: 5px;
}

[dir="rtl"] .el-icon--left {
  margin-right: 0;
  margin-left: 5px;
}

/* 下拉菜单RTL */
[dir="rtl"] .el-dropdown-menu {
  text-align: right;
}

/* 警告信息RTL */
[dir="rtl"] .el-alert {
  text-align: right;
}

/* 表单项RTL间距调整 */
[dir="rtl"] .el-form-item {
  margin-bottom: 22px;
}

[dir="rtl"] .el-form-item__content {
  text-align: right;
}

/* 单选按钮组RTL */
[dir="rtl"] .el-radio-group {
  direction: rtl;
}

[dir="rtl"] .el-radio {
  margin-left: 0;
  margin-right: 30px;
}

[dir="rtl"] .el-radio:last-child {
  margin-right: 0;
}

/* 日期选择器RTL */
[dir="rtl"] .el-date-editor {
  direction: ltr; /* 日期输入保持LTR格式 */
}

/* 文本域RTL */
[dir="rtl"] .el-textarea__inner {
  text-align: right;
}

/* 输入框RTL */
[dir="rtl"] .el-input__inner {
  text-align: right;
}

/* 卡片RTL */
[dir="rtl"] .el-card {
  text-align: right;
}

/* 成功页面RTL */
[dir="rtl"] .success-card {
  text-align: right;
}

[dir="rtl"] .gift-info {
  direction: rtl;
}

/* 产品摘要RTL */
[dir="rtl"] .selected-summary {
  text-align: right;
}

[dir="rtl"] .product-groups {
  direction: rtl;
}

[dir="rtl"] .group-summary {
  direction: rtl;
}

/* 加载状态RTL */
[dir="rtl"] .loading {
  text-align: right;
}

/* 无数据提示RTL */
[dir="rtl"] .no-data {
  text-align: right;
}

/* 响应式RTL调整 */
@media (max-width: 768px) {
  [dir="rtl"] .user-header {
    align-items: flex-end;
  }
  
  [dir="rtl"] .system-title {
    text-align: right;
  }
  
  [dir="rtl"] .user-info {
    align-items: flex-end;
  }
}

/* 阿拉伯语字体优化 */
[dir="rtl"] {
  font-family: 'Segoe UI', 'Helvetica Neue', Arial, 'Noto Sans Arabic', 'Arabic UI Text', sans-serif;
}

/* 阿拉伯语数字和英文保持LTR */
[dir="rtl"] .number,
[dir="rtl"] .english-text {
  direction: ltr;
  display: inline-block;
}

/* 混合文本处理 */
[dir="rtl"] .mixed-content {
  unicode-bidi: plaintext;
}
