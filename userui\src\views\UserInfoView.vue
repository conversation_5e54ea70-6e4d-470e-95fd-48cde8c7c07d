<template>
  <div class="user-info-container">
    <div class="page-container">
      <!-- 如果用户已提交，显示成功页面 -->
      <div v-if="userStore.hasSubmitted" class="success-container">
        <div class="success-card">
          <div class="success-icon">
            <div class="success-emoji">✅</div>
          </div>
          <h1 class="success-title">{{ $t('end.title') }}</h1>
          <p class="success-message">{{ $t('end.message') }}</p>
          <div class="success-actions">
            <el-button type="primary" size="large" @click="goToLogin">
              {{ $t('end.backToLogin') }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 如果用户未提交，显示表单 -->
      <div v-else>
        <div class="header">
          <h1>{{ $t('userInfo.title') }}</h1>
          <p class="subtitle">{{ $t('userInfo.subtitle') }}</p>
        </div>

        <div class="form-container">
          <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" size="large">
          <el-form-item :label="$t('userInfo.gender')" prop="UserSex">
            <el-radio-group v-model="form.UserSex">
              <el-radio value="男">{{ $t('userInfo.male') }}</el-radio>
              <el-radio value="女">{{ $t('userInfo.female') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="$t('userInfo.age')" prop="Age">
            <el-radio-group v-model="form.Age">
              <el-radio value="20岁以下">{{ $t('userInfo.ageUnder20') }}</el-radio>
              <el-radio value="20-30岁">{{ $t('userInfo.age20to30') }}</el-radio>
              <el-radio value="31-40岁">{{ $t('userInfo.age31to40') }}</el-radio>
              <el-radio value="41-50岁">{{ $t('userInfo.age41to50') }}</el-radio>
              <el-radio value="50岁以上">{{ $t('userInfo.ageOver50') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="$t('userInfo.country')" prop="Country">
            <el-input v-model="form.Country" :placeholder="$t('userInfo.countryPlaceholder')" />
          </el-form-item>
          </el-form>
        </div>

        <div class="actions">
          <el-button size="large" @click="skip">{{ $t('common.skip') }}</el-button>
          <el-button type="primary" size="large" @click="submit" :loading="submitting">
            {{ $t('common.submit') }}
          </el-button>
        </div>

        <!-- 选择的产品摘要 -->
        <div class="selected-summary" v-if="selectedProductsCount > 0">
          <h3>{{ $t('userInfo.selectedProducts') }}</h3>
          <p>{{ $t('userInfo.totalSelected', { count: selectedProductsCount }) }}</p>
          <div class="product-groups">
            <div v-for="(products, groupIndex) in selectedProductsByGroup" :key="groupIndex" class="group-summary">
              <span class="group-label">{{ $t('userInfo.groupLabel', { index: groupIndex }) }}</span>
              <span class="product-count">{{ products.length }} {{ $t('products.productCount', { count: products.length }) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElForm } from 'element-plus'
import { useSurveyStore } from '@/stores/survey'
import { useUserStore } from '@/stores/user'
import axios from 'axios'

const router = useRouter()
const surveyStore = useSurveyStore()
const userStore = useUserStore()
const { t } = useI18n()
const formRef = ref<InstanceType<typeof ElForm>>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  UserSex: '',
  Country: '',
  Age: ''
})

// 表单验证规则
const rules = computed(() => ({
  UserSex: [
    { required: true, message: t('userInfo.genderRequired'), trigger: 'change' }
  ],
  Age: [
    { required: true, message: t('userInfo.ageRequired'), trigger: 'change' }
  ],
  Country: [
    { required: true, message: t('userInfo.countryRequired'), trigger: 'blur' }
  ]
}))

// 计算属性
const selectedProductsByGroup = computed(() => {
  return surveyStore.getSelectedProductsByGroup()
})

const selectedProductsCount = computed(() => {
  return surveyStore.selectedProducts.length
})

// 跳过填写
const skip = async () => {
  try {
    console.log('跳过填写，当前路由:', router.currentRoute.value.path)
    console.log('当前选择的产品:', surveyStore.selectedProducts)

    // 检查是否有选择的产品
    if (surveyStore.selectedProducts.length === 0) {
      ElMessage.warning(t('products.selectProductFirst'))
      return
    }

    // 准备跳过请求数据
    const skipData = {
      selectedProductIds: surveyStore.selectedProducts.map(p => p.id),
      thirdPartyId: userStore.currentUser?.providerId || '',
      provider: userStore.currentUser?.provider || ''
    }

    console.log('跳过请求数据:', skipData)

    // 调用跳过 API，只保存投票数据
    const response = await axios.post('/api/User/SkipUserInfo', skipData)
    console.log('跳过 API 响应:', response.data)

    if (response.data?.success) {
      ElMessage.success(t('userInfo.skipSuccess'))

      // 保存用户ID（重要：跳过用户信息的用户仍然需要userId来提交联系信息）
      if (response.data.userId) {
        userStore.setUserId(response.data.userId)
        console.log('跳过用户信息，保存userId:', response.data.userId)
      }

      // 注意：不要在这里标记为已提交，让用户可以继续填写联系信息
      // userStore.markAsSubmitted() // 移除这行

      // 不要清空调研数据，保留产品选择信息
      // surveyStore.reset() // 移除这行

      // 跳转到结束页面
      console.log('开始路由跳转...')
      try {
        const result = await router.replace('/end')
        console.log('路由跳转结果:', result)
        console.log('跳转后当前路由:', router.currentRoute.value.path)
      } catch (routeError) {
        console.error('路由跳转异常:', routeError)
        // 尝试使用 window.location 作为备选方案
        window.location.href = '/end'
      }
    } else {
      console.error('跳过失败:', response.data)
      ElMessage.error(response.data?.message || t('userInfo.skipFailed'))
    }
  } catch (error) {
    console.error('跳过失败:', error)
    ElMessage.error(t('userInfo.skipFailed'))
  }
}

// 提交表单
const submit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) {
      console.log('表单验证失败')
      return
    }

    submitting.value = true
    console.log('开始提交表单')

    // 准备提交数据 - 转换字段名以匹配后端API
    const submitData = {
      userSex: form.UserSex,
      country: form.Country,
      age: form.Age,
      selectedProductIds: surveyStore.selectedProducts.map(p => p.id),
      thirdPartyId: userStore.currentUser?.providerId || '',
      provider: userStore.currentUser?.provider || ''
    }

    console.log('提交数据:', submitData)

    // 提交用户信息和选择的产品
    const response = await axios.post('/api/User/SubmitUserInfo', submitData)
    console.log('API响应:', response.data)

    if (response.data?.success) {
      ElMessage.success('提交成功！请填写联系信息以便我们寄送小礼物！')

      // 保存用户ID到store，用于后续联系信息提交
      userStore.setUserId(response.data.userId)

      // 跳转到提交成功页面（现在包含联系信息表单）
      console.log('准备跳转到提交成功页面')
      console.log('提交成功，当前路由:', router.currentRoute.value.path)
      try {
        const result = await router.replace('/end')
        console.log('路由跳转结果:', result)
        console.log('跳转后当前路由:', router.currentRoute.value.path)
      } catch (routeError) {
        console.error('路由跳转异常:', routeError)
        // 尝试使用 window.location 作为备选方案
        window.location.href = '/end'
      }
    } else {
      console.error('提交失败:', response.data)
      ElMessage.error(response.data?.message || t('userInfo.submitFailed'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(t('userInfo.submitFailed'))
  } finally {
    submitting.value = false
  }
}

// 返回登录页面
const goToLogin = () => {
  // 清除用户状态
  userStore.logout()
  // 跳转到登录页面
  router.push('/login')
}

// 组件挂载时的调试信息
onMounted(() => {
  console.log('UserInfoView 组件已挂载')
  console.log('当前路由:', router.currentRoute.value.path)
  console.log('用户是否已提交:', userStore.hasSubmitted)
  console.log('选择的产品数量:', surveyStore.selectedProducts.length)
  console.log('选择的产品:', surveyStore.selectedProducts)
})
</script>

<style scoped>
.user-info-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.page-container {
  width: 100%;
  max-width: 600px;
  padding: 40px 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-container {
    padding: 20px 15px;
  }
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.subtitle {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.form-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.actions .el-button {
  padding: 12px 30px;
  min-width: 120px;
}

/* 移动端表单适配 */
@media (max-width: 768px) {
  .form-container {
    padding: 20px;
    margin-bottom: 20px;
  }

  .actions {
    gap: 15px;
    margin-bottom: 30px;
  }

  .actions .el-button {
    flex: 1;
    min-width: auto;
  }

  .header h1 {
    font-size: 24px;
  }
}

.selected-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.selected-summary h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.product-groups {
  margin-top: 15px;
}

.group-summary {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.group-summary:last-child {
  border-bottom: none;
}

.group-label {
  color: #606266;
  font-weight: 500;
}

.product-count {
  color: #409eff;
  font-weight: 500;
}

/* 成功页面样式 */
.success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.success-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.success-icon {
  margin-bottom: 30px;
}

.success-emoji {
  font-size: 80px;
  line-height: 1;
}

.success-title {
  font-size: 32px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
}

.success-message {
  font-size: 18px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 40px;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .success-card {
    padding: 40px 20px;
    margin: 0 10px;
  }

  .success-title {
    font-size: 24px;
  }

  .success-message {
    font-size: 16px;
  }
}
</style>
