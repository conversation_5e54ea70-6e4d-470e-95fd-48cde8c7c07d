import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      port: 5173, // usersurvey 端口
      host: true,
      strictPort: true,
      proxy: {
        '/api': {
          target: (() => {
            // 根据 VITE_USE_HTTPS 自动选择后端地址
            const useHttps = env.VITE_USE_HTTPS === 'true'
            const target = useHttps ? 'https://localhost:8081' : 'http://localhost:8080'
            console.log(`🔄 UserSurvey 代理目标: ${target}`)
            return target
          })(),
          changeOrigin: true,
          secure: false, // 允许自签名证书
          ws: true, // 支持 WebSocket
          configure: (proxy, options) => {
            // 添加错误处理，当目标服务不可用时自动切换
            proxy.on('error', (err, req, res) => {
              const currentTarget = options.target
              const fallbackTarget = currentTarget?.includes('8081')
                ? 'http://localhost:8080'
                : 'https://localhost:8081'

              console.log(`❌ UserSurvey 代理错误: ${err.message}`)
              console.log(`🔄 UserSurvey 尝试切换到备用目标: ${fallbackTarget}`)

              // 注意：这里只是记录错误，实际的切换需要重启开发服务器
              console.log(`💡 UserSurvey 提示: 请检查后端服务或重启开发服务器`)
            })
          }
        }
      }
    }
  }
})
