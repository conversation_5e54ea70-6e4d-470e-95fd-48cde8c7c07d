import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
    server: {
      port: 3000,
      host: true,
      strictPort: true,
      proxy: {
        '/api': {
          target: (() => {
            // 根据 VITE_USE_HTTPS 自动选择后端地址
            const useHttps = env.VITE_USE_HTTPS === 'true'
            const target = useHttps ? 'https://localhost:8081' : 'http://localhost:8080'
            console.log(`🔄 UserUI 代理目标: ${target}`)
            return target
          })(),
          changeOrigin: true,
          secure: false, // 允许自签名证书
          ws: true, // 支持 WebSocket
        }
      }
    }
  }
})
