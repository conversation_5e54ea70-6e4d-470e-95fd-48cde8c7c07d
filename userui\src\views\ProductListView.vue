<template>
  <div class="product-container">
    <div class="page-container">
      <!-- 用户信息栏 -->
      <div class="user-header">
        <div class="system-title">
          <h1>用户调研系统</h1>
        </div>
        <div class="user-info" v-if="userStore.currentUser">
          <div class="user-avatar">
            <img v-if="userStore.currentUser.avatar" :src="userStore.currentUser.avatar" :alt="userStore.currentUser.userName" />
            <div v-else class="avatar-placeholder">{{ userStore.currentUser.userName.charAt(0) }}</div>
          </div>
          <div class="user-details">
            <span class="user-name">{{ userStore.currentUser.userName }}</span>
            <span class="user-provider">{{ userStore.currentUser.provider === 'google' ? 'Google' : 'Facebook' }} 登录</span>
          </div>
          <el-button type="danger" text @click="handleLogout">
            退出登录
          </el-button>
        </div>
      </div>

      <div class="header">
        <h2>{{ $t('products.title') }}</h2>
        <div class="group-info" v-if="surveyStore.currentGroup">
          <span>{{ $t('products.groupInfo', { current: surveyStore.currentGroup.groupIndex, total: surveyStore.productGroups.length }) }}</span>
          <span class="product-count">{{ $t('products.productCount', { count: surveyStore.currentProducts.length }) }}</span>
        </div>
      </div>



      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <el-loading-directive />
        <p>{{ $t('common.loading') }}</p>
      </div>

    <!-- 产品网格 -->
    <div v-else-if="surveyStore.currentProducts.length > 0" class="product-grid">
      <div
        v-for="product in surveyStore.currentProducts"
        :key="product.id"
        :class="['product-card', {
          selected: surveyStore.isProductSelected(product),
          disabled: !surveyStore.canSelectMore && !surveyStore.isProductSelected(product)
        }]"
      >
        <!-- 爱心按钮 - 只有点击这里才会选择产品 -->
        <div
          class="heart-button"
          @click.stop="toggleProduct(product)"
          :class="{ disabled: !surveyStore.canSelectMore && !surveyStore.isProductSelected(product) }"
        >
          <div
            :class="['heart-icon', { active: surveyStore.isProductSelected(product) }]"
          >
            ♥
          </div>
        </div>

        <!-- 产品图片 - 点击显示预览 -->
        <img
          class="product-img"
          :src="getThumbnailUrl(product)"
          :alt="product.name"
          @click="showImagePreview(product)"
          @error="handleImageError"
        />
      </div>
    </div>

      <!-- 无数据提示 -->
      <div v-else class="no-data">
        <p>{{ $t('products.noData') }}</p>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation-section">
        <!-- 都不喜欢按钮 -->
        <el-button
          type="default"
          size="large"
          @click="handleDislikeAll"
          class="nav-button dislike-button"
        >
          {{ $t('products.dislikeAll') }}
        </el-button>

        <!-- 提交按钮 -->
        <el-button
          type="primary"
          size="large"
          @click="handleSubmit"
          :disabled="surveyStore.currentGroupSelectedCount > surveyStore.maxSelectionsPerGroup"
          class="nav-button submit-button"
        >
          {{ $t('common.submit') }}
          <span v-if="surveyStore.currentGroupSelectedCount > 0" class="selection-count-badge">
            ({{ surveyStore.currentGroupSelectedCount }})
          </span>
        </el-button>
      </div>

      <!-- 提示信息 -->
      <div v-if="surveyStore.isLastGroup && !surveyStore.canSubmit" class="warning-section">
        <el-alert
          :title="$t('products.selectAtLeastOne')"
          type="warning"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 图片预览 - 支持缩放和触摸 -->
    <div
      v-if="imagePreviewVisible"
      class="pure-image-preview"
      @click="closeImagePreview"
      @keyup.esc="closeImagePreview"
      @wheel="handleImageZoom"
      @touchstart="handlePreviewTouchStart"
      @touchend="handlePreviewTouchEnd"
      tabindex="0"
    >
      <div class="image-container" @click.stop>
        <img
          v-if="previewProduct"
          :src="getOriginalImageUrl(previewProduct)"
          :alt="previewProduct.name"
          class="enlarged-image"
          :style="imageTransformStyle"
          @error="handleImageError"
          @click="closeImagePreview"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        />

        <!-- 爱心按钮 -->
        <div
          class="floating-heart-button"
          @click.stop="toggleProductFromPreviewAndClose"
          :class="{
            active: surveyStore.isProductSelected(previewProduct),
            disabled: !surveyStore.canSelectMore && !surveyStore.isProductSelected(previewProduct)
          }"
        >
          <div class="heart-icon">♥</div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useSurveyStore } from '@/stores/survey'
import { useUserStore } from '@/stores/user'
import { InfoFilled, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const surveyStore = useSurveyStore()
const userStore = useUserStore()
const { t } = useI18n()
const loading = ref(false)

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewProduct = ref<any>(null)

// 图片缩放相关
const imageScale = ref(1)
const imageTranslateX = ref(0)
const imageTranslateY = ref(0)
const imageTransformStyle = ref({})

// 触摸相关
const touchStartDistance = ref(0)
const touchStartScale = ref(1)
const touchStartX = ref(0)
const touchStartY = ref(0)
const lastTouchX = ref(0)
const lastTouchY = ref(0)
const touchStartTime = ref(0)
const touchStartPosition = ref({ x: 0, y: 0 })
const touchMoved = ref(false)

// 获取产品分组数据
const fetchProductGroups = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/Product/ListByGroup')

    // 设置产品分组数据到store
    surveyStore.setProductGroups(response.data)

    console.log('获取产品分组成功:', response.data)
  } catch (error) {
    console.error('获取产品分组失败:', error)
    ElMessage.error('获取产品数据失败')
  } finally {
    loading.value = false
  }
}

// 切换产品选择状态
const toggleProduct = (product: { id: number; name: string; imageUrl?: string; groupIndex: number; sortOrder: number }) => {
  if (surveyStore.isProductSelected(product)) {
    surveyStore.unselectProduct(product)
  } else {
    if (surveyStore.canSelectMore) {
      surveyStore.selectProduct(product)
    } else {
      ElMessage.warning(t('products.selectionHint', { max: surveyStore.maxSelectionsPerGroup }))
    }
  }
}

// 处理提交
const handleSubmit = () => {
  // 检查当前页面是否选择了产品
  if (surveyStore.currentGroupSelectedCount === 0) {
    ElMessage.warning(t('products.noProductSelected'))
    return
  }

  // 检查是否选择超过5个产品
  if (surveyStore.currentGroupSelectedCount > surveyStore.maxSelectionsPerGroup) {
    ElMessage.warning(t('products.tooManySelected', { max: surveyStore.maxSelectionsPerGroup }))
    return
  }

  if (surveyStore.isLastGroup) {
    // 最后一组，检查是否至少有一个组选择了产品
    if (!surveyStore.canSubmit) {
      ElMessage.warning(t('products.selectAtLeastOne'))
      return
    }
    // 跳转到补充资料页面
    router.push('/user-info')
  } else {
    // 不是最后一组，跳转到下一组
    surveyStore.nextGroup()
  }
}

// 处理都不喜欢
const handleDislikeAll = () => {
  ElMessageBox.confirm(
    t('products.dislikeAllConfirm'),
    t('products.dislikeAllTitle'),
    {
      confirmButtonText: t('common.confirm'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
    }
  ).then(() => {
    // 清除当前组的所有选择
    surveyStore.clearCurrentGroupSelections()

    // 跳转到下一组或完成调研
    if (surveyStore.isLastGroup) {
      // 如果是最后一组且没有任何选择，跳转到补充资料页面
      router.push('/user-info')
    } else {
      surveyStore.nextGroup()
    }
  }).catch(() => {
    // 用户取消，不做任何操作
  })
}

// 处理上一组
const handlePreviousGroup = () => {
  surveyStore.previousGroup()
}

// 动态获取API基础URL
const getApiBaseUrl = () => {
  if (import.meta.env.DEV) {
    // 开发环境：根据 VITE_USE_HTTPS 选择
    const useHttps = import.meta.env.VITE_USE_HTTPS === 'true'
    return useHttps ? 'https://localhost:8081' : 'http://localhost:8080'
  }

  // 生产环境：自动适配当前页面协议
  const currentProtocol = window.location.protocol.replace(':', '')
  const host = import.meta.env.VITE_API_HOST || 'localhost'

  // 根据当前页面协议选择对应的后端端口
  const port = currentProtocol === 'https' ? 8081 : 8080
  return `${currentProtocol}://${host}:${port}`
}

// 获取缩略图URL，用于列表显示
const getThumbnailUrl = (product: any) => {
  console.log('处理产品缩略图URL:', product.name, product.imageUrl)

  if (!product.imageUrl) {
    console.log('产品无图片URL，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是example.com的无效链接，直接使用占位图片
  if (product.imageUrl.includes('example.com')) {
    console.log('检测到example.com无效链接，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是相对路径，转换为缩略图URL
  if (product.imageUrl.startsWith('/pic/')) {
    const fileName = product.imageUrl.split('/').pop()
    // 动态获取API基础URL
    const apiBaseUrl = getApiBaseUrl()
    const thumbnailUrl = `${apiBaseUrl}/thumbnails/${fileName}`
    console.log('转换为缩略图URL:', thumbnailUrl)
    return thumbnailUrl
  }

  // 如果是localhost的完整URL，转换为缩略图URL
  if (product.imageUrl.includes('localhost') && product.imageUrl.includes('/pic/')) {
    const fileName = product.imageUrl.split('/').pop()
    const apiBaseUrl = getApiBaseUrl()
    const thumbnailUrl = `${apiBaseUrl}/thumbnails/${fileName}`
    console.log('转换完整URL为缩略图URL:', thumbnailUrl)
    return thumbnailUrl
  }

  console.log('使用原始图片URL作为缩略图:', product.imageUrl)
  return product.imageUrl
}

// 获取原图URL，用于预览显示
const getOriginalImageUrl = (product: any) => {
  console.log('处理产品原图URL:', product.name, product.imageUrl)

  if (!product.imageUrl) {
    console.log('产品无图片URL，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是example.com的无效链接，直接使用占位图片
  if (product.imageUrl.includes('example.com')) {
    console.log('检测到example.com无效链接，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是相对路径，转换为完整的本地URL
  if (product.imageUrl.startsWith('/pic/')) {
    // 动态获取API基础URL
    const apiBaseUrl = getApiBaseUrl()
    const fullUrl = `${apiBaseUrl}${product.imageUrl}`
    console.log('转换相对路径为完整URL:', fullUrl)
    return fullUrl
  }

  // 如果是localhost但端口不对，修正端口
  if (product.imageUrl.includes('localhost:5028') || product.imageUrl.includes('localhost:5029') || product.imageUrl.includes('localhost:7292') || product.imageUrl.includes('localhost:8080') || product.imageUrl.includes('localhost:8081')) {
    // 动态获取正确的API基础URL
    const apiBaseUrl = getApiBaseUrl()
    const urlParts = new URL(product.imageUrl)
    const correctedUrl = `${apiBaseUrl}${urlParts.pathname}`
    console.log('修正端口号:', correctedUrl)
    return correctedUrl
  }

  // 如果是本地localhost的链接，检查端口是否正确
  if (product.imageUrl.includes('localhost:8080') || product.imageUrl.includes('localhost:8081')) {
    const apiBaseUrl = getApiBaseUrl()
    // 如果URL中的端口与当前配置匹配，直接返回
    if (product.imageUrl.startsWith(apiBaseUrl)) {
      console.log('使用本地图片URL:', product.imageUrl)
      return product.imageUrl
    } else {
      // 端口不匹配，修正URL
      const urlParts = new URL(product.imageUrl)
      const correctedUrl = `${apiBaseUrl}${urlParts.pathname}`
      console.log('修正本地图片URL端口:', correctedUrl)
      return correctedUrl
    }
  }

  console.log('使用原始图片URL:', product.imageUrl)
  return product.imageUrl
}

// 保持向后兼容的方法
const getImageUrl = getOriginalImageUrl

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  const productName = img.alt
  const originalSrc = img.src
  const placeholderUrl = `https://placehold.co/120x180?text=${encodeURIComponent(productName)}`

  console.error('图片加载失败:', {
    productName,
    originalSrc,
    placeholderUrl
  })

  img.src = placeholderUrl
}

// 退出登录
const handleLogout = () => {
  userStore.logout()
  ElMessage.success(t('products.logoutSuccess'))
  router.push('/login')
}

// 显示图片预览
const showImagePreview = (product: any) => {
  previewProduct.value = product
  imagePreviewVisible.value = true
  // 重置缩放状态
  resetImageTransform()
}

// 关闭图片预览
const closeImagePreview = () => {
  imagePreviewVisible.value = false
  previewProduct.value = null
  // 重置缩放状态
  resetImageTransform()
}

// 重置图片变换状态
const resetImageTransform = () => {
  imageScale.value = 1
  imageTranslateX.value = 0
  imageTranslateY.value = 0
  updateImageTransform()
}

// 更新图片变换样式
const updateImageTransform = () => {
  imageTransformStyle.value = {
    transform: `scale(${imageScale.value}) translate(${imageTranslateX.value}px, ${imageTranslateY.value}px)`,
    transition: 'transform 0.3s ease'
  }
}

// 从预览对话框中切换产品选择状态
const toggleProductFromPreview = () => {
  if (previewProduct.value) {
    toggleProduct(previewProduct.value)
  }
}

// 从预览对话框中切换产品选择状态并关闭预览
const toggleProductFromPreviewAndClose = () => {
  if (previewProduct.value) {
    toggleProduct(previewProduct.value)
    // 延迟关闭，让用户看到选择效果
    setTimeout(() => {
      closeImagePreview()
    }, 300)
  }
}

// 鼠标滚轮缩放处理
const handleImageZoom = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(0.5, Math.min(3, imageScale.value + delta))
  imageScale.value = newScale
  updateImageTransform()
}

// 触摸开始处理
const handleTouchStart = (event: TouchEvent) => {
  event.preventDefault()

  // 记录触摸开始时间和位置，用于判断是否为简单点击
  touchStartTime.value = Date.now()
  touchMoved.value = false

  if (event.touches.length === 2) {
    // 双指缩放
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    touchStartDistance.value = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )
    touchStartScale.value = imageScale.value
  } else if (event.touches.length === 1) {
    // 单指操作
    const touch = event.touches[0]
    touchStartX.value = touch.clientX
    touchStartY.value = touch.clientY
    touchStartPosition.value = { x: touch.clientX, y: touch.clientY }
    lastTouchX.value = imageTranslateX.value
    lastTouchY.value = imageTranslateY.value
  }
}

// 触摸移动处理
const handleTouchMove = (event: TouchEvent) => {
  event.preventDefault()

  // 检测是否有移动，用于判断是否为简单点击
  if (event.touches.length === 1) {
    const touch = event.touches[0]
    const moveDistance = Math.sqrt(
      Math.pow(touch.clientX - touchStartPosition.value.x, 2) +
      Math.pow(touch.clientY - touchStartPosition.value.y, 2)
    )
    if (moveDistance > 10) { // 移动超过10px认为是拖拽
      touchMoved.value = true
    }
  }

  if (event.touches.length === 2) {
    // 双指缩放
    touchMoved.value = true
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    const currentDistance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )
    const scale = (currentDistance / touchStartDistance.value) * touchStartScale.value
    imageScale.value = Math.max(0.5, Math.min(3, scale))
    updateImageTransform()
  } else if (event.touches.length === 1 && imageScale.value > 1 && touchMoved.value) {
    // 单指拖拽（仅在放大时且确实在移动）
    const touch = event.touches[0]
    const deltaX = touch.clientX - touchStartX.value
    const deltaY = touch.clientY - touchStartY.value
    imageTranslateX.value = lastTouchX.value + deltaX / imageScale.value
    imageTranslateY.value = lastTouchY.value + deltaY / imageScale.value
    updateImageTransform()
  }
}

// 触摸结束处理
const handleTouchEnd = (event: TouchEvent) => {
  event.preventDefault()

  // 检测是否为简单点击（tap）
  const touchEndTime = Date.now()
  const touchDuration = touchEndTime - touchStartTime.value

  // 如果触摸时间短且没有移动，认为是简单点击，关闭预览
  if (touchDuration < 300 && !touchMoved.value && event.changedTouches.length === 1) {
    // 检查点击位置是否在爱心按钮上
    const touch = event.changedTouches[0]
    const heartButton = document.querySelector('.floating-heart-button')

    if (heartButton) {
      const rect = heartButton.getBoundingClientRect()
      const isClickOnHeart = touch.clientX >= rect.left &&
                            touch.clientX <= rect.right &&
                            touch.clientY >= rect.top &&
                            touch.clientY <= rect.bottom

      // 如果不是点击爱心按钮，则关闭预览
      if (!isClickOnHeart) {
        closeImagePreview()
      }
    } else {
      // 如果找不到爱心按钮，直接关闭预览
      closeImagePreview()
    }
  }

  // 重置触摸状态
  touchStartDistance.value = 0
  touchStartScale.value = 1
  touchMoved.value = false
}

// 预览容器触摸事件处理
const previewTouchStartTime = ref(0)
const previewTouchMoved = ref(false)
const previewTouchStartPosition = ref({ x: 0, y: 0 })

const handlePreviewTouchStart = (event: TouchEvent) => {
  if (event.touches.length === 1) {
    previewTouchStartTime.value = Date.now()
    previewTouchMoved.value = false
    const touch = event.touches[0]
    previewTouchStartPosition.value = { x: touch.clientX, y: touch.clientY }
  }
}

const handlePreviewTouchEnd = (event: TouchEvent) => {
  if (event.changedTouches.length === 1) {
    const touchEndTime = Date.now()
    const touchDuration = touchEndTime - previewTouchStartTime.value
    const touch = event.changedTouches[0]

    // 计算移动距离
    const moveDistance = Math.sqrt(
      Math.pow(touch.clientX - previewTouchStartPosition.value.x, 2) +
      Math.pow(touch.clientY - previewTouchStartPosition.value.y, 2)
    )

    // 如果是短时间的轻触且没有移动，关闭预览
    if (touchDuration < 300 && moveDistance < 10) {
      // 检查是否点击在爱心按钮上
      const heartButton = document.querySelector('.floating-heart-button')
      if (heartButton) {
        const rect = heartButton.getBoundingClientRect()
        const isClickOnHeart = touch.clientX >= rect.left &&
                              touch.clientX <= rect.right &&
                              touch.clientY >= rect.top &&
                              touch.clientY <= rect.bottom

        if (!isClickOnHeart) {
          closeImagePreview()
        }
      } else {
        closeImagePreview()
      }
    }
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && imagePreviewVisible.value) {
    closeImagePreview()
  }
}

// 组件挂载时获取数据和添加事件监听
onMounted(() => {
  fetchProductGroups()
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.product-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  gap: 15px;
}

/* 移动端用户头部适配 */
@media (max-width: 768px) {
  .user-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    padding: 15px 0;
  }

  .system-title h1 {
    font-size: 24px;
  }

  .user-info {
    align-self: stretch;
    justify-content: space-between;
  }
}

.system-title {
  flex: 1;
}

.system-title h1 {
  margin: 0;
  color: #409eff;
  font-size: 32px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #409eff;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.user-provider {
  font-size: 12px;
  color: #909399;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #606266;
  font-size: 14px;
}

.product-count {
  color: #909399;
  font-size: 12px;
}



.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
  min-height: 300px;
  padding: 0 10px;
}

/* 响应式网格布局 */
@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);  /* 移动端固定两列 */
    gap: 10px;
    padding: 0 5px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 22px;
  }
}

@media (min-width: 1025px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 25px;
  }
}

.product-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 280px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px 0 rgba(0,0,0,0.15);
}

.product-card.selected {
  border-color: transparent;
  background: #fff;  /* 保持白色背景 */
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);  /* 保持原始阴影 */
  transform: none;  /* 去掉位移效果 */
}

.product-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);
}

.heart-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  transition: transform 0.2s ease;
}

.heart-button:hover {
  transform: scale(1.05);
}

.heart-button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.heart-button.disabled:hover {
  transform: none;
}

.heart-icon {
  color: #d1d5db;
  font-size: 24px;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.heart-icon.active {
  color: #ef4444;
  transform: scale(1.2);
  background: linear-gradient(135deg, #fff 0%, #ffe6e6 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  animation: heartPulse 1.5s ease-in-out infinite;
}

@keyframes heartPulse {
  0%, 100% {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  }
  50% {
    transform: scale(1.3);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.6);
  }
}

.heart-icon:hover {
  color: #f87171;
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
}

.product-img {
  width: 160px;
  height: 240px;
  object-fit: contain;
  border-radius: 8px;
  margin: 10px;
  transition: transform 0.3s ease;
  background: #f8f9fa;
}

.product-card:hover .product-img {
  transform: scale(1.02);
}

/* 移动端图片适配 */
@media (max-width: 480px) {
  .product-img {
    width: 120px;
    height: 180px;
    margin: 6px;
  }

  .product-card {
    min-height: 220px;
    padding: 10px;
  }

  .heart-icon {
    width: 28px;
    height: 28px;
    font-size: 20px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .product-img {
    width: 150px;
    height: 225px;
    margin: 10px;
  }
}



.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
  font-size: 16px;
}

.navigation-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.nav-button {
  padding: 15px 40px;
  font-size: 16px;
  font-weight: 600;
  min-width: 120px;
}

.dislike-button {
  background-color: #f56565;
  border-color: #f56565;
  color: white;
}

.dislike-button:hover {
  background-color: #e53e3e;
  border-color: #e53e3e;
}

.submit-button {
  position: relative;
}

.selection-count-badge {
  margin-left: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 14px;
}

.warning-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.warning-section .el-alert {
  max-width: 400px;
}

/* 移动端导航按钮适配 */
@media (max-width: 768px) {
  .navigation-section {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .nav-button {
    width: 100%;
    order: 0;
  }

  .dislike-button {
    order: 1;
  }

  .submit-button {
    order: 2;
  }
}

/* 平板端导航按钮适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .navigation-section {
    gap: 15px;
  }

  .nav-button {
    min-width: 140px;
  }
}

/* 纯图片预览 - 无背景放大效果 */
.pure-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  outline: none;
  background: rgba(0, 0, 0, 0.9);
  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  user-select: none;
}

.image-container {
  position: relative;
  cursor: default;
}

.enlarged-image {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  cursor: pointer;
  display: block;
  user-select: none;
  -webkit-user-drag: none;
  touch-action: none;
}

/* 移动端图片预览优化 */
@media (max-width: 768px) {
  .enlarged-image {
    max-width: 95vw;
    max-height: 85vh;
  }

  .floating-heart-button {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
}

/* 浮动爱心按钮 */
.floating-heart-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  user-select: none;
}

.floating-heart-button:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.floating-heart-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.floating-heart-button.disabled:hover {
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
}

.heart-icon {
  font-size: 36px;
  color: #ddd;
  transition: all 0.3s ease;
  user-select: none;
}

.floating-heart-button.active .heart-icon {
  color: #ff4757;
  animation: heartBeat 0.6s ease-in-out;
}

.floating-heart-button:hover .heart-icon {
  color: #ff6b7a;
}

.floating-heart-button.active:hover .heart-icon {
  color: #ff4757;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 移动端预览适配 */
@media (max-width: 768px) {
  .preview-heart-button {
    width: 60px;
    height: 60px;
  }

  .preview-heart-icon {
    font-size: 28px;
  }

  .image-preview-container {
    min-height: 70vh;
  }

  .preview-image {
    max-height: 60vh;
  }
}

/* 产品图片点击提示 */
.product-img {
  cursor: pointer;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.product-img:hover {
  transform: scale(1.02);
  opacity: 0.9;
}
</style>