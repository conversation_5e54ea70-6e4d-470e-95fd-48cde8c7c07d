# 生产环境配置 (Production Environment)

# ==================== 环境标识 ====================
NODE_ENV=production
VITE_ENV=production

# ==================== API 配置 ====================
# 生产环境 - 自动适配当前页面协议
# 无需手动配置，会自动检测页面协议并选择对应后端端口
VITE_API_HOST=localhost
VITE_API_TIMEOUT=15000

# API端点配置
VITE_API_AUTH_ENDPOINT=/api/auth
VITE_API_USER_ENDPOINT=/api/user
VITE_API_PRODUCT_ENDPOINT=/api/product
VITE_API_ADMIN_ENDPOINT=/api/admin
VITE_API_UPLOAD_ENDPOINT=/api/upload

# ==================== 前端配置 ====================
# 前端服务地址
VITE_APP_BASE_URL=https://localhost:3000
VITE_APP_HOST=localhost
VITE_APP_PORT=3000
VITE_APP_PROTOCOL=https

# ==================== OAuth 配置 ====================
# 前端只保留公开信息，敏感信息移到后端
# Google OAuth 公开配置
VITE_GOOGLE_CLIENT_ID=************-su2s5e82v75beq482m3dbb964rstp8ud.apps.googleusercontent.com
VITE_GOOGLE_AUTH_URL=https://accounts.google.com/o/oauth2/v2/auth
VITE_GOOGLE_SCOPE=openid email profile

# Facebook OAuth 公开配置
VITE_FACEBOOK_APP_ID=****************
VITE_FACEBOOK_AUTH_URL=https://www.facebook.com/v18.0/dialog/oauth
VITE_FACEBOOK_SCOPE=email,public_profile

# OAuth 回调配置 - 统一指向后端
VITE_OAUTH_CALLBACK_BASE_URL=https://localhost:8081/api/auth

# ==================== 安全配置 ====================
# HTTPS 配置
VITE_FORCE_HTTPS=true
VITE_SSL_ENABLED=true

# CORS 配置
VITE_CORS_ENABLED=true
VITE_CORS_ORIGIN=https://localhost:3000

# ==================== 调试配置 ====================
# 生产模式配置
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error
VITE_SHOW_CONSOLE_LOGS=false

# ==================== 其他配置 ====================
# 文件上传配置
VITE_UPLOAD_MAX_SIZE=5242880
VITE_UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# 缓存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=3600000
