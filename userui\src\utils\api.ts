/**
 * API工具函数
 * 提供统一的API调用方法
 */

import { getApiUrl, getRequestConfig, apiConfig, getEndpointUrl } from '@/config/api'

// 通用API请求函数
export const apiRequest = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  try {
    const url = getApiUrl(endpoint)
    const config = getRequestConfig(options)
    
    console.log(`API请求: ${options.method || 'GET'} ${url}`)
    
    const response = await fetch(url, config)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error(`API错误: ${response.status} - ${errorText}`)
      throw new Error(`API请求失败: ${response.status} - ${errorText}`)
    }
    
    const result = await response.json()
    console.log(`API响应:`, result)
    
    return result
  } catch (error) {
    console.error('API请求异常:', error)
    throw error
  }
}

// GET请求
export const apiGet = <T = any>(endpoint: string, params?: Record<string, any>): Promise<T> => {
  let url = endpoint
  if (params) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    url += `?${searchParams.toString()}`
  }
  
  return apiRequest<T>(url, { method: 'GET' })
}

// POST请求
export const apiPost = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiRequest<T>(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined
  })
}

// PUT请求
export const apiPut = <T = any>(endpoint: string, data?: any): Promise<T> => {
  return apiRequest<T>(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined
  })
}

// DELETE请求
export const apiDelete = <T = any>(endpoint: string): Promise<T> => {
  return apiRequest<T>(endpoint, { method: 'DELETE' })
}

// 文件上传请求
export const apiUpload = <T = any>(endpoint: string, formData: FormData): Promise<T> => {
  const url = getApiUrl(endpoint)
  
  return fetch(url, {
    method: 'POST',
    body: formData
    // 注意：不要设置Content-Type，让浏览器自动设置
  }).then(response => {
    if (!response.ok) {
      throw new Error(`上传失败: ${response.status}`)
    }
    return response.json()
  })
}

// OAuth相关API
export const oauthApi = {
  // Google登录
  googleLogin: (code: string, redirectUri: string) =>
    apiPost(apiConfig.endpoints.auth_routes.google, { code, redirectUri }),

  // Facebook登录
  facebookLogin: (code: string, redirectUri: string) =>
    apiPost(apiConfig.endpoints.auth_routes.facebook, { code, redirectUri }),

  // 检查提交状态
  checkSubmission: (thirdPartyId: string, provider: string) =>
    apiGet(apiConfig.endpoints.auth_routes.checkSubmission, { thirdPartyId, provider })
}

// 用户相关API
export const userApi = {
  // 获取用户资料
  getProfile: () => apiGet('/api/user/profile'),
  
  // 检查用户状态
  checkStatus: (thirdPartyId: string, provider: string) => 
    apiGet('/api/user/CheckSubmissionStatus', { thirdPartyId, provider }),
  
  // 更新用户信息
  updateProfile: (data: any) => apiPut('/api/user/update', data)
}

// 产品相关API
export const productApi = {
  // 获取产品列表
  getList: () => apiGet('/api/product'),
  
  // 获取产品详情
  getDetail: (id: string) => apiGet(`/api/product/${id}`),
  
  // 上传产品图片
  uploadImage: (formData: FormData) => apiUpload(apiConfig.endpoints.product_routes.upload, formData)
}

// 管理员相关API
export const adminApi = {
  // 获取用户列表
  getUsers: () => apiGet(apiConfig.endpoints.admin_routes.users),

  // 获取统计数据
  getStatistics: () => apiGet(apiConfig.endpoints.admin_routes.statistics),

  // 获取产品管理数据
  getProducts: () => apiGet(apiConfig.endpoints.admin_routes.products)
}

// 检查API配置
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    // 尝试访问一个简单的端点来检查API是否可用
    const response = await fetch(getApiUrl('/api/health'), { 
      method: 'GET',
      timeout: 5000 
    } as any)
    return response.ok
  } catch (error) {
    console.warn('API健康检查失败:', error)
    return false
  }
}
