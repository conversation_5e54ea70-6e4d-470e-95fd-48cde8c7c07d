# Facebook登录配置完整指南

## 🎯 第一步：创建Facebook应用

### 1. 访问Facebook开发者平台
- 打开 [Facebook for Developers](https://developers.facebook.com/)
- 使用您的Facebook账户登录

### 2. 创建新应用
1. 点击 "我的应用" → "创建应用"
2. 选择应用类型：**"消费者"** 或 **"无"**
3. 填写应用信息：
   - **应用名称**: 用户调研系统
   - **应用联系邮箱**: 您的邮箱地址
   - **应用用途**: 选择适合的用途

### 3. 添加Facebook登录产品
1. 在应用面板中，点击 "添加产品"
2. 找到 "Facebook 登录" 并点击 "设置"
3. 选择 "Web" 平台

## 🔧 第二步：配置Facebook登录

### 1. 基本设置
在 Facebook登录 → 设置 页面中：

#### 有效OAuth重定向URI
添加以下URI：
```
http://localhost:3000/auth/facebook/callback
http://localhost:3001/auth/facebook/callback
https://yourdomain.com/auth/facebook/callback  (生产环境)
```

#### 客户端OAuth设置
- ✅ 启用 "Web OAuth登录"
- ✅ 启用 "强制Web OAuth重新验证"

### 2. 应用域名设置
在 应用设置 → 基本 页面中：
- **应用域名**: `localhost` (开发环境)
- **隐私政策URL**: 您的隐私政策链接
- **服务条款URL**: 您的服务条款链接

### 3. 获取应用凭据
在 应用设置 → 基本 页面中找到：
- **应用编号 (App ID)**: 这是您的客户端ID
- **应用密钥 (App Secret)**: 点击"显示"获取

## 📝 第三步：更新配置文件

### 1. 前端配置 (.env)
```env
# Facebook OAuth 配置
VITE_FACEBOOK_APP_ID=您的Facebook应用编号
VITE_FACEBOOK_REDIRECT_URI=http://localhost:3000/auth/facebook/callback
```

### 2. 后端配置 (appsettings.json)
```json
{
  "OAuth": {
    "Facebook": {
      "AppId": "您的Facebook应用编号",
      "AppSecret": "您的Facebook应用密钥"
    }
  }
}
```

## 🔒 第四步：权限配置

### 1. 应用审核
对于生产环境，需要提交以下权限审核：
- `email` - 获取用户邮箱
- `public_profile` - 获取基本资料

### 2. 测试用户
在开发阶段，可以添加测试用户：
1. 进入 角色 → 测试用户
2. 添加测试用户进行功能测试

## 🌐 第五步：域名验证（生产环境）

### 1. 域名验证
1. 在 应用设置 → 基本 中添加您的域名
2. 下载HTML验证文件或添加Meta标签
3. 完成域名所有权验证

### 2. 应用审核
1. 填写应用详细信息
2. 提供应用截图和使用说明
3. 提交审核（通常需要几天时间）

## 🧪 第六步：测试配置

### 1. 开发环境测试
```bash
# 启动前端
npm run dev

# 启动后端
dotnet run
```

### 2. 测试流程
1. 访问登录页面
2. 点击Facebook登录按钮
3. 验证是否正确跳转到Facebook
4. 完成登录并检查回调处理

## ⚠️ 常见问题解决

### 1. "应用未上线"错误
- 确保应用状态为"上线"
- 检查域名配置是否正确

### 2. "重定向URI不匹配"错误
- 检查OAuth重定向URI配置
- 确保URI完全匹配（包括协议和端口）

### 3. "权限不足"错误
- 确保请求的权限已获得审核
- 检查应用是否处于开发模式

## 📋 配置检查清单

- [ ] Facebook应用已创建
- [ ] Facebook登录产品已添加
- [ ] OAuth重定向URI已配置
- [ ] 应用域名已设置
- [ ] App ID和App Secret已获取
- [ ] 前端.env文件已更新
- [ ] 后端appsettings.json已更新
- [ ] 测试用户已添加（可选）
- [ ] 登录流程测试通过

## 🔗 有用链接

- [Facebook开发者文档](https://developers.facebook.com/docs/facebook-login/web)
- [Facebook登录最佳实践](https://developers.facebook.com/docs/facebook-login/security)
- [应用审核指南](https://developers.facebook.com/docs/app-review)
