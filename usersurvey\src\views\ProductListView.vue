<template>
  <div class="product-container">
    <!-- 系统标题 -->
    <div class="system-title">
      <h1>用户调研系统</h1>
    </div>

    <div class="header">
      <h2>产品选择</h2>
      <div class="group-info" v-if="surveyStore.currentGroup">
        <span>分组 {{ surveyStore.currentGroup.groupIndex }} / {{ surveyStore.productGroups.length }}</span>
        <span class="product-count">（{{ surveyStore.currentProducts.length }} 个产品）</span>
      </div>
    </div>

    <!-- 选择提示 -->
    <div class="selection-info">
      <div class="selection-count">
        已选择 {{ surveyStore.currentGroupSelectedCount }} / {{ surveyStore.maxSelectionsPerGroup }} 个产品
      </div>
      <div class="selection-hint">
        <el-icon><InfoFilled /></el-icon>
        <span>每个分组最多可选择 {{ surveyStore.maxSelectionsPerGroup }} 个产品</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading" v-loading="loading" element-loading-text="加载中...">
      <p>正在加载产品数据...</p>
    </div>

    <!-- 产品网格 -->
    <div v-else-if="surveyStore.currentProducts.length > 0" class="product-grid">
      <div
        v-for="product in surveyStore.currentProducts"
        :key="product.id"
        :class="['product-card', {
          selected: surveyStore.isProductSelected(product),
          disabled: !surveyStore.canSelectMore && !surveyStore.isProductSelected(product)
        }]"
        @click="toggleProduct(product)"
      >
        <!-- 爱心按钮 -->
        <div class="heart-button">
          <div
            :class="['heart-icon', { active: surveyStore.isProductSelected(product) }]"
          >
            ♥
          </div>
        </div>

        <img
          class="product-img"
          :src="getThumbnailUrl(product)"
          :alt="product.name"
          @error="handleImageError"
        />
        <div class="product-info">
          <div class="product-name">{{ product.name }}</div>
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="no-data">
      <p>暂无产品数据</p>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation-section">
      <!-- 上一组按钮 -->
      <el-button
        v-if="surveyStore.hasPreviousGroup()"
        type="default"
        size="large"
        @click="handlePreviousGroup"
        class="nav-button"
      >
        <el-icon><ArrowLeft /></el-icon>
        上一组
      </el-button>

      <!-- 占位元素，保持布局平衡 -->
      <div v-else class="nav-placeholder"></div>

      <!-- 下一组/提交按钮 -->
      <el-button
        type="primary"
        size="large"
        @click="handleSubmit"
        :disabled="surveyStore.isLastGroup && !surveyStore.canSubmit"
        class="nav-button"
      >
        {{ surveyStore.isLastGroup ? '提交' : '下一组' }}
      </el-button>
    </div>

    <!-- 提示信息 -->
    <div v-if="surveyStore.isLastGroup && !surveyStore.canSubmit" class="warning-section">
      <el-alert
        title="请至少在一个分组中选择产品"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSurveyStore } from '@/stores/survey'
import { InfoFilled, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const surveyStore = useSurveyStore()
const loading = ref(false)

// 获取产品分组数据
const fetchProductGroups = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/Product/ListByGroup')

    // 设置产品分组数据到store
    surveyStore.setProductGroups(response.data)

    console.log('获取产品分组成功:', response.data)
  } catch (error) {
    console.error('获取产品分组失败:', error)
    ElMessage.error('获取产品数据失败')
  } finally {
    loading.value = false
  }
}

// 切换产品选择状态
const toggleProduct = (product: { id: number; name: string; imageUrl?: string; groupIndex: number; sortOrder: number }) => {
  if (surveyStore.isProductSelected(product)) {
    surveyStore.unselectProduct(product)
  } else {
    if (surveyStore.canSelectMore) {
      surveyStore.selectProduct(product)
    } else {
      ElMessage.warning(`每个分组最多只能选择 ${surveyStore.maxSelectionsPerGroup} 个产品`)
    }
  }
}

// 处理提交
const handleSubmit = () => {
  if (surveyStore.isLastGroup) {
    // 最后一组，检查是否至少有一个组选择了产品
    if (!surveyStore.canSubmit) {
      ElMessage.warning('请至少在一个分组中选择产品')
      return
    }
    // 跳转到补充资料页面
    router.push('/user-info')
  } else {
    // 不是最后一组，跳转到下一组（允许当前组不选择任何产品）
    surveyStore.nextGroup()
  }
}

// 处理上一组
const handlePreviousGroup = () => {
  surveyStore.previousGroup()
}

// 获取缩略图URL，用于列表显示
const getThumbnailUrl = (product: any) => {
  console.log('处理产品缩略图URL:', product.name, product.imageUrl)

  if (!product.imageUrl) {
    console.log('产品无图片URL，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是example.com的无效链接，直接使用占位图片
  if (product.imageUrl.includes('example.com')) {
    console.log('检测到example.com无效链接，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是相对路径，转换为缩略图URL
  if (product.imageUrl.startsWith('/pic/')) {
    const fileName = product.imageUrl.split('/').pop()
    const apiBaseUrl = getApiBaseUrl()
    const thumbnailUrl = `${apiBaseUrl}/thumbnails/${fileName}`
    console.log('转换为缩略图URL:', thumbnailUrl)
    return thumbnailUrl
  }

  // 如果是localhost的完整URL，转换为缩略图URL
  if (product.imageUrl.includes('localhost') && product.imageUrl.includes('/pic/')) {
    const fileName = product.imageUrl.split('/').pop()
    const apiBaseUrl = getApiBaseUrl()
    const thumbnailUrl = `${apiBaseUrl}/thumbnails/${fileName}`
    console.log('转换完整URL为缩略图URL:', thumbnailUrl)
    return thumbnailUrl
  }

  console.log('使用原始图片URL作为缩略图:', product.imageUrl)
  return product.imageUrl
}

// 动态获取API基础URL
const getApiBaseUrl = () => {
  if (import.meta.env.DEV) {
    // 开发环境：根据 VITE_USE_HTTPS 选择
    const useHttps = import.meta.env.VITE_USE_HTTPS === 'true'
    return useHttps ? 'https://localhost:8081' : 'http://localhost:8080'
  }

  // 生产环境：自动适配当前页面协议
  const currentProtocol = window.location.protocol.replace(':', '')
  const host = import.meta.env.VITE_API_HOST || 'localhost'

  // 根据当前页面协议选择对应的后端端口
  const port = currentProtocol === 'https' ? 8081 : 8080
  return `${currentProtocol}://${host}:${port}`
}

// 获取原图URL，用于预览显示
const getImageUrl = (product: any) => {
  console.log('处理产品原图URL:', product.name, product.imageUrl)

  if (!product.imageUrl) {
    console.log('产品无图片URL，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是example.com的无效链接，直接使用占位图片
  if (product.imageUrl.includes('example.com')) {
    console.log('检测到example.com无效链接，使用占位图片')
    return `https://placehold.co/120x180?text=${encodeURIComponent(product.name)}`
  }

  // 如果是相对路径，转换为完整的本地URL
  if (product.imageUrl.startsWith('/pic/')) {
    // 动态获取API基础URL
    const apiBaseUrl = getApiBaseUrl()
    const fullUrl = `${apiBaseUrl}${product.imageUrl}`
    console.log('转换相对路径为完整URL:', fullUrl)
    return fullUrl
  }

  // 如果是localhost但端口不对，修正端口
  if (product.imageUrl.includes('localhost:5028') || product.imageUrl.includes('localhost:5029') || product.imageUrl.includes('localhost:7292') || product.imageUrl.includes('localhost:8080') || product.imageUrl.includes('localhost:8081')) {
    // 动态获取正确的API基础URL
    const apiBaseUrl = getApiBaseUrl()
    const urlParts = new URL(product.imageUrl)
    const correctedUrl = `${apiBaseUrl}${urlParts.pathname}`
    console.log('修正端口号:', correctedUrl)
    return correctedUrl
  }

  // 如果是本地localhost的链接，检查端口是否正确
  if (product.imageUrl.includes('localhost:8080') || product.imageUrl.includes('localhost:8081')) {
    const apiBaseUrl = getApiBaseUrl()
    // 如果URL中的端口与当前配置匹配，直接返回
    if (product.imageUrl.startsWith(apiBaseUrl)) {
      console.log('使用本地图片URL:', product.imageUrl)
      return product.imageUrl
    } else {
      // 端口不匹配，修正URL
      const urlParts = new URL(product.imageUrl)
      const correctedUrl = `${apiBaseUrl}${urlParts.pathname}`
      console.log('修正本地图片URL端口:', correctedUrl)
      return correctedUrl
    }
  }

  console.log('使用原始图片URL:', product.imageUrl)
  return product.imageUrl
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  const productName = img.alt
  const originalSrc = img.src
  const placeholderUrl = `https://placehold.co/120x180?text=${encodeURIComponent(productName)}`

  console.error('图片加载失败:', {
    productName,
    originalSrc,
    placeholderUrl
  })

  img.src = placeholderUrl
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProductGroups()
})
</script>

<style scoped>
.product-container {
  padding: 40px 20px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 60px);
  background: #f5f5f5;
}

.system-title {
  text-align: left;
  margin-bottom: 30px;
  padding: 0;
}

.system-title h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 400;
  color: #409eff;
  letter-spacing: 0.5px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0;
  background: transparent;
  backdrop-filter: none;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 400;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 400;
  padding: 0;
  background: transparent;
  border-radius: 0;
  border: none;
}

.product-count {
  color: #999;
  font-size: 12px;
  font-weight: 400;
}

.selection-info {
  background: transparent;
  backdrop-filter: none;
  border: none;
  border-radius: 0;
  padding: 0;
  margin-bottom: 20px;
  box-shadow: none;
}

.selection-count {
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin-bottom: 0;
}

.selection-hint {
  display: none;
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  background: white;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #e0e0e0;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 40px;
  min-height: 400px;
}

.product-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 280px;
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.product-card::before {
  display: none;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.product-card:hover::before {
  display: none;
}

.product-card.selected {
  border-color: #409eff;
  background: white;
  transform: none;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.product-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  filter: none;
}

.product-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-card.disabled::before {
  display: none;
}

.heart-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.heart-icon {
  color: #ccc;
  font-size: 20px;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.heart-icon.active {
  color: #ff4757;
  transform: none;
  background: white;
  box-shadow: 0 1px 3px rgba(255, 71, 87, 0.2);
  animation: none;
}

.heart-icon:hover {
  color: #ff6b7a;
  transform: none;
  background: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.product-img {
  width: 120px;
  height: 160px;
  object-fit: contain;
  border-radius: 4px;
  margin-bottom: 15px;
  margin-top: 10px;
  box-shadow: none;
  transition: none;
  position: relative;
  z-index: 2;
  border: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.product-card:hover .product-img {
  box-shadow: none;
}

.product-info {
  text-align: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.product-name {
  font-weight: 400;
  color: #333;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.4;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-size: 16px;
  background: white;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #e0e0e0;
}

.navigation-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding: 0;
  background: transparent;
  backdrop-filter: none;
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.nav-button {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 400;
  min-width: 100px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

:deep(.el-button--primary) {
  background: #409eff;
  border: 1px solid #409eff;
}

:deep(.el-button--primary:hover) {
  background: #66b1ff;
  border: 1px solid #66b1ff;
  transform: none;
  box-shadow: none;
}

.nav-placeholder {
  width: 100px;
}

.warning-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.warning-section .el-alert {
  max-width: 400px;
  border-radius: 4px;
  backdrop-filter: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .product-container {
    padding: 20px 15px;
  }

  .system-title h1 {
    font-size: 1.8rem;
  }

  .header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
    padding: 0;
  }

  .header h2 {
    font-size: 1.3rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
  }

  .product-card {
    min-height: 240px;
    padding: 15px;
  }

  .product-img {
    width: 100px;
    height: 140px;
  }

  .navigation-section {
    flex-direction: column;
    gap: 15px;
    padding: 0;
  }

  .nav-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .system-title h1 {
    font-size: 1.6rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 12px;
  }

  .product-card {
    min-height: 220px;
    padding: 12px;
  }

  .product-img {
    width: 90px;
    height: 120px;
  }

  .heart-icon {
    width: 25px;
    height: 25px;
    font-size: 16px;
  }

  .selection-info {
    padding: 0;
  }
}


</style>