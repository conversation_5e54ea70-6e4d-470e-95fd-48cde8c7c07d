export default {
  // Common
  common: {
    submit: 'Submit',
    skip: 'Skip',
    next: 'Next Group',
    previous: 'Previous Group',
    loading: 'Loading...',
    logout: 'Logout',
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    select: 'Select',
    unselect: 'Unselect',
    logoutSuccess: 'Logged out successfully'
  },
  
  // Login page
  login: {
    title: 'User Research System',
    subtitle: 'Please select login method',
    googleLogin: 'Login with Google',
    facebookLogin: 'Login with Facebook',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed, please try again',
    loggingIn: 'Logging in...',
    facebookNotConfigured: 'Facebook Not Configured',
    facebookConfigNotice: 'Facebook Login Not Configured',
    facebookConfigDesc: 'Facebook login requires App ID configuration to work.',
    facebookConfigGuide: 'Please refer to FACEBOOK_SETUP_GUIDE.md for configuration.',
    inAppBrowserWarning: 'In-app browser detected, recommend copying link to system browser for best login experience',
    configError: 'Configuration Error',
    facebookNotConfiguredError: 'Facebook login not configured, please contact administrator to configure Facebook App ID',
    inAppBrowserDetected: 'In-app Browser Detected',
    inAppBrowserDesc: 'For successful login, recommend opening in system browser',
    viewLoginGuide: 'View Login Guide',
    copyPageLink: 'Copy Page Link',
    pageLinkCopied: 'Page link copied to clipboard',
    recommendSystemBrowser: 'Recommend logging in with system browser for best experience'
  },
  
  // Product selection page
  products: {
    title: 'Product Selection',
    groupInfo: 'Group {current} / {total}',
    productCount: '({count} products)',
    selectedCount: 'Selected {selected} / {max} products',
    selectionHint: 'You can select up to {max} products per group',
    noData: 'No product data available',
    selectAtLeastOne: 'Please select at least one product in a group',
    selectProductFirst: 'Please select products before skipping',
    logoutSuccess: 'Logged out successfully',
    dislikeAll: 'Dislike All',
    dislikeAllTitle: 'Confirm Action',
    dislikeAllConfirm: 'Are you sure you dislike all products?',
    tooManySelected: 'You can select at most {max} products',
    noProductSelected: 'No products selected currently'
  },
  
  // User info page
  userInfo: {
    title: 'Complete Profile',
    subtitle: 'Please fill in your basic information to help us better understand your needs',
    name: 'Name',
    namePlaceholder: 'Please enter your name',
    nameRequired: 'Please enter your name',
    gender: 'Gender',
    genderRequired: 'Please select gender',
    male: 'Male',
    female: 'Female',
    age: 'Age',
    ageRequired: 'Please select age',
    ageUnder20: 'Under 20',
    age20to30: '20-30 years old',
    age31to40: '31-40 years old',
    age41to50: '41-50 years old',
    ageOver50: 'Over 50',
    country: 'Country',
    countryPlaceholder: 'Please enter your country',
    countryRequired: 'Please enter country',
    address: 'Address',
    addressPlaceholder: 'Please enter your detailed address',
    selectedProducts: 'Your Selected Products',
    totalSelected: 'Total {count} products selected',
    groupLabel: 'Group {index}:',
    submitSuccess: 'Submitted successfully! Thank you for your help, we will send you a small gift as appreciation!',
    skipSuccess: 'Voting data saved, thank you for your participation!',
    submitFailed: 'Submission failed, please try again',
    skipFailed: 'Skip failed, please try again'
  },

  // End page
  end: {
    title: 'Submission Successful!',
    message: 'Thank you for participating in our product research!<br>Your valuable feedback will help us provide better products and services.',
    giftInfo: 'We will send you a small gift as appreciation!',
    contactTitle: 'Contact Information',
    contactSubtitle: 'Please fill in your contact information so we can send you a small gift as appreciation!',
    contactName: 'Name',
    contactNamePlaceholder: 'Please enter your name',
    contactNameRequired: 'Please enter your name',
    contactPhone: 'Contact',
    contactPhonePlaceholder: 'Please enter your contact information',
    contactPhoneRequired: 'Please enter contact information',
    contactAddress: 'Address',
    contactAddressPlaceholder: 'Please enter detailed mailing address',
    contactAddressRequired: 'Please enter mailing address',
    submitSuccess: 'Submitted successfully! We will send you a gift soon!',
    skipSuccess: 'Thank you for your participation!',
    submitFailed: 'Submission failed, please try again',
    skipFailed: 'Skip failed, please try again',
    finalMessage: 'Thank you again for your participation! We will process your information as soon as possible.',
    backToLogin: 'Back to Login'
  },
  
  // Final completion page
  final: {
    title: 'Survey Completed',
    message: 'Thank you for your participation! You have successfully completed the survey.',
    info: 'To switch accounts, please logout first',
    backToLogin: 'Back to Login'
  },

  // Already submitted page
  alreadySubmitted: {
    title: 'Survey Already Completed',
    message: 'Thank you for your participation! You have already successfully submitted survey data. Each user can only participate once.',
    info: 'To switch accounts, please logout first'
  },

  // Service unavailable page
  serviceUnavailable: {
    title: 'Service Temporarily Unavailable',
    description: 'We apologize, but the survey system is currently closed and unable to provide service.',
    subDescription: 'The system will automatically detect service status and redirect to login page once restored. Click refresh to check system status.',
    refresh: 'Refresh Page',
    contact: 'If you have any questions, please contact the system administrator',
    systemRestored: 'Survey system has been restored, redirecting...',
    systemStillClosed: 'Survey system is still closed, please try again later',
    checkFailed: 'Status check failed, please try again later'
  },

  // Error messages
  errors: {
    networkError: 'Network error, please check your connection',
    serverError: 'Server error, please try again later',
    unknownError: 'Unknown error, please try again'
  },

  // Authentication callback page
  auth: {
    processing: 'Processing Login Information',
    processingDesc: 'Please wait, we are verifying your identity...',
    loginSuccess: 'Login successful! Redirecting...',
    loginFailed: 'Login failed, please try again',
    networkError: 'Network connection failed, please check network and try again',
    serverError: 'Server temporarily unavailable, please try again later',
    authError: 'Authorization failed, please login again',
    noAuthCode: 'No authorization code received',
    securityError: 'Security verification failed, please login again',
    backendError: 'Backend response error',
    userCancelled: 'Authorization cancelled, returning to login page',
    noAuthCode: 'No authorization code received',
    securityError: 'Security verification failed, please login again',
    loginSuccess: 'Login successful! Redirecting...',
    loginFailed: 'Login failed',
    networkError: 'Network connection failed, please check network and try again',
    serverError: 'Server temporarily unavailable, please try again later',
    authError: 'Authorization failed, please login again'
  },

  // Mobile login guide page
  mobileGuide: {
    title: '📱 Mobile Login Guide',
    subtitle: 'For the best login experience, please follow these steps',
    environmentDetection: '🔍 Current Environment Detection',
    deviceType: 'Device Type:',
    mobileDevice: 'Mobile Device',
    desktopDevice: 'Desktop Device',
    browserEnvironment: 'Browser Environment:',
    recommendedMethod: 'Recommended Method:',
    systemBrowser: 'System Browser',
    wechatBrowser: 'WeChat Built-in Browser',
    qqBrowser: 'QQ Built-in Browser',
    alipayBrowser: 'Alipay Built-in Browser',
    inAppBrowser: 'In-App Browser',
    directLogin: 'Direct Login',
    copyToSystemBrowser: 'Copy link to system browser',
    mobileOptimizedLogin: 'Mobile optimized login',
    inAppBrowserGuide: '⚠️ In-App Browser Login Guide',
    inAppWarning: 'Detected that you are using an in-app browser (such as WeChat, QQ, etc.), which may affect third-party login functionality.',
    step1Title: 'Copy current page link',
    step1Desc: 'Click the button below to copy the page link',
    copyLink: '📋 Copy Link',
    step2Title: 'Open in system browser',
    step2Desc: 'Open your phone\'s system browser (Safari, Chrome, etc.), paste the link and visit',
    step3Title: 'Complete login',
    step3Desc: 'Click the Google login button in the system browser to complete authentication',
    mobileTips: '📱 Mobile Login Tips',
    tip1: 'Ensure stable network connection',
    tip2: 'Allow browser popups (if needed)',
    tip3: 'Do not switch apps during login',
    openedInSystemBrowser: 'I have opened in system browser',
    startLogin: 'Start Login',
    confirmed: '✅ Confirmed',
    confirmCopied: 'Confirm link copied',
    backToHome: 'Back to Home',
    linkCopied: 'Link copied to clipboard',
    pleaseConfirmFirst: 'Please copy the link and open it in system browser first'
  }
}
