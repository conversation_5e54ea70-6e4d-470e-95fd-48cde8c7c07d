<!DOCTYPE html>
<html>
<head>
    <title>OAuth取消授权测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>OAuth取消授权测试页面</h1>
    <p>这个页面用于测试各种OAuth取消授权的情况</p>
    
    <div class="test-section">
        <h2>模拟OAuth回调URL</h2>
        <p>点击下面的按钮模拟不同的OAuth回调情况：</p>
        
        <button class="test-button" onclick="testUserCancelled()">用户取消授权 (access_denied)</button>
        <button class="test-button" onclick="testUserDenied()">用户拒绝 (user_denied)</button>
        <button class="test-button" onclick="testCancelledByUser()">用户取消 (cancelled_by_user)</button>
        <button class="test-button" onclick="testInvalidRequest()">无效请求 (invalid_request)</button>
        <button class="test-button" onclick="testServerError()">服务器错误 (server_error)</button>
        <button class="test-button" onclick="testSuccess()">成功授权</button>
    </div>
    
    <div class="test-section">
        <h2>直接测试错误处理函数</h2>
        <input type="text" id="errorInput" placeholder="输入错误代码" style="width: 300px; padding: 5px;">
        <button class="test-button" onclick="testErrorHandler()">测试错误处理</button>
        <div id="errorResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.insertBefore(resultDiv, results.firstChild);
        }

        function testUserCancelled() {
            const url = `/auth/google/callback?error=access_denied&state=test123`;
            addResult(`模拟用户取消授权: ${url}`, 'info');
            window.location.href = url;
        }

        function testUserDenied() {
            const url = `/auth/google/callback?error=user_denied&state=test123`;
            addResult(`模拟用户拒绝: ${url}`, 'info');
            window.location.href = url;
        }

        function testCancelledByUser() {
            const url = `/auth/facebook/callback?error=cancelled_by_user&state=test123`;
            addResult(`模拟用户取消: ${url}`, 'info');
            window.location.href = url;
        }

        function testInvalidRequest() {
            const url = `/auth/google/callback?error=invalid_request&state=test123`;
            addResult(`模拟无效请求: ${url}`, 'info');
            window.location.href = url;
        }

        function testServerError() {
            const url = `/auth/facebook/callback?error=server_error&state=test123`;
            addResult(`模拟服务器错误: ${url}`, 'info');
            window.location.href = url;
        }

        function testSuccess() {
            const url = `/auth/google/callback?code=test_auth_code&state=test123`;
            addResult(`模拟成功授权: ${url}`, 'success');
            window.location.href = url;
        }

        function testErrorHandler() {
            const errorInput = document.getElementById('errorInput');
            const errorResult = document.getElementById('errorResult');
            const error = errorInput.value.trim();
            
            if (!error) {
                alert('请输入错误代码');
                return;
            }

            // 模拟错误处理函数的逻辑
            const cancelErrors = [
                'access_denied', 'user_cancelled', 'user_denied', 
                'cancelled_by_user', 'authorization_declined', 
                'consent_required', 'user_cancelled_login', 
                'authorization_cancelled', 'login_cancelled'
            ];
            
            const isUserCancelled = cancelErrors.some(cancelError => 
                error.toLowerCase().includes(cancelError.toLowerCase())
            );
            
            let friendlyMessage;
            if (isUserCancelled) {
                friendlyMessage = '已取消授权，返回登录页面';
            } else {
                const errorMap = {
                    'invalid_request': '请求参数错误，请重试',
                    'invalid_client': '应用配置错误，请联系管理员',
                    'invalid_grant': '授权码无效，请重新登录',
                    'server_error': '服务器错误，请稍后重试',
                    'network_error': '网络连接失败，请检查网络后重试'
                };
                
                friendlyMessage = errorMap[error.toLowerCase()] || `登录失败: ${error}`;
            }
            
            errorResult.innerHTML = `
                <strong>错误代码:</strong> ${error}<br>
                <strong>是否用户取消:</strong> ${isUserCancelled ? '是' : '否'}<br>
                <strong>友好提示:</strong> ${friendlyMessage}<br>
                <strong>处理方式:</strong> ${isUserCancelled ? '立即跳转' : '延迟跳转'}
            `;
            errorResult.style.display = 'block';
            errorResult.className = `result ${isUserCancelled ? 'info' : 'error'}`;
            
            addResult(`测试错误处理: ${error} -> ${isUserCancelled ? '用户取消' : '其他错误'}`, 
                     isUserCancelled ? 'info' : 'error');
        }

        // 页面加载时显示当前URL参数
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');
            const code = urlParams.get('code');
            
            if (error || code) {
                addResult(`当前URL参数 - error: ${error}, code: ${code}`, 'info');
            }
        };
    </script>
</body>
</html>
