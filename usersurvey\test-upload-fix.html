<!DOCTYPE html>
<html>
<head>
    <title>测试图片上传修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #337ecc; }
        .success { color: green; }
        .error { color: red; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; margin: 20px 0; text-align: center; }
        .image-preview { max-width: 200px; max-height: 200px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>图片上传修复测试</h1>
    
    <div class="upload-area">
        <input type="file" id="fileInput" accept="image/*" />
        <br><br>
        <button onclick="testUpload()">测试上传图片</button>
        <button onclick="testDeleteImage()">测试删除图片</button>
        <button onclick="testOtherAPI()">测试其他API</button>
    </div>
    
    <div id="result"></div>
    <div id="imagePreview"></div>

    <script>
        let currentFileName = '';
        let currentImageUrl = '';

        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('result').innerHTML = '<p class="error">请先选择一个图片文件</p>';
                return;
            }

            try {
                document.getElementById('result').innerHTML = '<p>正在上传图片...</p>';
                
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/api/Imgs/Img', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    currentFileName = result.fileName;
                    currentImageUrl = result.url;
                    
                    document.getElementById('result').innerHTML = 
                        '<h3 class="success">图片上传成功!</h3>' +
                        '<p>文件名: ' + result.fileName + '</p>' +
                        '<p>原始名: ' + result.originalName + '</p>' +
                        '<p>文件大小: ' + (result.fileSize / 1024).toFixed(2) + ' KB</p>' +
                        '<p>URL: ' + result.url + '</p>';
                    
                    document.getElementById('imagePreview').innerHTML = 
                        '<img src="' + result.url + '" class="image-preview" alt="上传的图片" />';
                        
                    // 测试上传后其他API是否还能正常工作
                    setTimeout(testOtherAPI, 1000);
                } else {
                    document.getElementById('result').innerHTML = 
                        '<h3 class="error">图片上传失败:</h3><p>' + result.message + '</p>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3 class="error">上传错误:</h3><p>' + error.message + '</p>';
            }
        }

        async function testDeleteImage() {
            if (!currentFileName) {
                document.getElementById('result').innerHTML = '<p class="error">没有可删除的图片，请先上传一张图片</p>';
                return;
            }

            try {
                document.getElementById('result').innerHTML = '<p>正在删除图片...</p>';
                
                const response = await fetch('/api/Imgs/DeleteImg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        fileName: currentFileName
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('result').innerHTML = 
                        '<h3 class="success">图片删除成功!</h3>';
                    document.getElementById('imagePreview').innerHTML = '';
                    currentFileName = '';
                    currentImageUrl = '';
                } else {
                    document.getElementById('result').innerHTML = 
                        '<h3 class="error">图片删除失败:</h3><p>' + result.message + '</p>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<h3 class="error">删除错误:</h3><p>' + error.message + '</p>';
            }
        }

        async function testOtherAPI() {
            try {
                document.getElementById('result').innerHTML += '<br><p>正在测试其他API是否正常...</p>';
                
                // 测试获取产品列表
                const response = await fetch('/api/Product/ListByGroup');
                const result = await response.json();
                
                document.getElementById('result').innerHTML += 
                    '<p class="success">✓ 其他API工作正常 - 获取到 ' + (result.length || 0) + ' 个产品分组</p>';
                    
                // 测试获取用户列表
                const userResponse = await fetch('/api/User/GetAllUsers');
                const userResult = await userResponse.json();
                
                if (userResult.success) {
                    document.getElementById('result').innerHTML += 
                        '<p class="success">✓ 用户API工作正常 - 获取到 ' + (userResult.data?.length || 0) + ' 个用户</p>';
                } else {
                    document.getElementById('result').innerHTML += 
                        '<p class="error">✗ 用户API异常: ' + userResult.message + '</p>';
                }
                
            } catch (error) {
                document.getElementById('result').innerHTML += 
                    '<p class="error">✗ 其他API测试失败: ' + error.message + '</p>';
            }
        }

        // 页面加载时的说明
        window.onload = function() {
            document.getElementById('result').innerHTML = 
                '<h3>测试说明:</h3>' +
                '<ol>' +
                '<li>选择一张图片文件</li>' +
                '<li>点击"测试上传图片"</li>' +
                '<li>观察上传后其他API是否还能正常工作</li>' +
                '<li>点击"测试删除图片"验证删除功能</li>' +
                '<li>再次测试其他API确认服务器状态正常</li>' +
                '</ol>';
        };
    </script>
</body>
</html>
