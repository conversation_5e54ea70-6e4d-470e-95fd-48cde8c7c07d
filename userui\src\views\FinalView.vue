<template>
  <div class="final-container">
    <div class="page-container">
      <div class="success-card">
        <div class="success-icon">
          <el-icon :size="80" color="#67c23a">
            <SuccessFilled />
          </el-icon>
        </div>

        <h1 class="success-title">{{ $t('final.title') }}</h1>

        <p class="success-message" v-html="$t('final.message')">
        </p>

        <div class="info-section">
          <el-icon :size="24" color="#409eff">
            <InfoFilled />
          </el-icon>
          <span>{{ $t('final.info') }}</span>
        </div>

        <div class="actions">
          <el-button type="primary" size="large" @click="goToLogin">
            {{ $t('final.backToLogin') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { SuccessFilled, InfoFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useSurveyStore } from '@/stores/survey'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const surveyStore = useSurveyStore()

// 返回登录页面
const goToLogin = () => {
  // 清除用户状态
  userStore.logout()
  // 清空调研数据
  surveyStore.reset()
  // 跳转到登录页面
  router.push('/login')
}
</script>

<style scoped>
.final-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-container {
  width: 100%;
  max-width: 600px;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.success-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.success-icon {
  margin-bottom: 30px;
}

.success-title {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.success-message {
  color: #606266;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 30px 0;
}

.info-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 30px;
  color: #1e40af;
  font-weight: 500;
}

.actions {
  margin-top: 30px;
}

.actions .el-button {
  padding: 12px 30px;
  font-size: 16px;
  min-width: 150px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .success-card {
    padding: 40px 30px;
    border-radius: 12px;
  }

  .success-title {
    font-size: 24px;
  }

  .success-message {
    font-size: 14px;
  }

  .info-section {
    padding: 12px;
    font-size: 14px;
  }

  .actions .el-button {
    width: 100%;
  }
}
</style>
