@import './base.css';

/* 全局应用样式 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-weight: normal;
}

/* 链接样式 */
a,
.green {
  text-decoration: none;
  color: #409eff;
  transition: all 0.3s ease;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: rgba(64, 158, 255, 0.1);
    border-radius: 4px;
  }
}

/* 容器通用样式 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px);
  background: #f5f5f5;
}

.content-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

/* 卡片通用样式 */
.card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 标题样式 */
.page-title {
  text-align: center;
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .content-wrapper {
    padding: 20px 15px;
  }

  .page-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 15px 10px;
  }

  .page-title {
    font-size: 1.75rem;
  }
}

/* Element Plus 全局样式优化 */
.el-button {
  border-radius: 4px !important;
  font-weight: 400 !important;
  transition: all 0.2s ease !important;
}

.el-button--primary {
  background: #409eff !important;
  border: 1px solid #409eff !important;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4) !important;
}

.el-input__wrapper {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s ease !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

.el-card {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
}

.el-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.el-alert {
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
}

.el-menu {
  border-radius: 12px !important;
}

.el-menu-item {
  border-radius: 8px !important;
  margin: 2px 4px !important;
  transition: all 0.3s ease !important;
}

.el-dialog {
  border-radius: 20px !important;
  backdrop-filter: blur(10px) !important;
}

.el-table {
  border-radius: 12px !important;
  overflow: hidden !important;
}

.el-pagination {
  justify-content: center !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}
