<template>
  <div class="mobile-login-container">
    <div class="page-container">
      <!-- 语言切换器 -->
      <div class="language-switcher-container">
        <LanguageSwitcher />
      </div>

      <div class="login-content">
        <el-card class="login-card">
          <h2 class="login-title">{{ $t('login.title') }}</h2>
          <p class="login-subtitle">{{ $t('login.subtitle') }}</p>
          


          <div class="login-buttons">
            <!-- Google 登录按钮 -->
            <el-button
              class="google-login-btn mobile-login-btn"
              size="large"
              :loading="loading"
              @click="handleGoogleLogin"
              :disabled="false"
            >
              <svg class="login-icon" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>{{ loading ? $t('login.loggingIn') : $t('login.googleLogin') }}</span>
            </el-button>

            <!-- Facebook 登录按钮 -->
            <el-button
              class="facebook-login-btn mobile-login-btn"
              size="large"
              :loading="loading"
              @click="handleFacebookLogin"
              :disabled="!configStatus.facebookReady"
            >
              <svg class="login-icon facebook-icon" viewBox="0 0 24 24">
                <path fill="white" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span>{{ loading ? $t('login.loggingIn') : $t('login.facebookLogin') }}</span>
            </el-button>
          </div>

          <!-- 应用内浏览器警告 -->
          <div class="in-app-warning" v-if="deviceInfo.isInApp">
            <div class="warning-content">
              <span class="warning-icon">⚠️</span>
              <div class="warning-text">
                <p><strong>{{ $t('login.inAppBrowserDetected') }}</strong></p>
                <p>{{ $t('login.inAppBrowserDesc') }}</p>
              </div>
            </div>
            <div class="warning-actions">
              <el-button @click="goToGuide" type="primary" size="small">
                {{ $t('login.viewLoginGuide') }}
              </el-button>
              <el-button @click="copyPageUrl" size="small">
                {{ $t('login.copyPageLink') }}
              </el-button>
            </div>
          </div>


        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { authService } from '@/services/authService'
import { isMobile, isInAppBrowser, validateAuthConfig } from '@/config/auth'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const router = useRouter()
const userStore = useUserStore()
const { t } = useI18n()

// 状态管理
const loading = ref(false)

// 配置状态
const configStatus = computed(() => {
  const validation = validateAuthConfig()
  return {
    googleReady: validation.googleConfigured,
    facebookReady: validation.facebookConfigured,
    hasErrors: !validation.isValid,
    hasWarnings: validation.hasWarnings
  }
})

// 设备信息
const deviceInfo = computed(() => ({
  isMobile: isMobile(),
  isInApp: isInAppBrowser()
}))



// Google 登录处理
const handleGoogleLogin = async () => {
  try {
    // 应用内浏览器提示
    if (deviceInfo.value.isInApp) {
      ElMessage.warning(t('login.recommendSystemBrowser'))
    }

    loading.value = true
    console.log('移动端Google登录开始...')

    // 验证配置
    const configValidation = validateAuthConfig()
    if (!configValidation.isValid) {
      throw new Error(`${t('login.configError')}: ${configValidation.errors.join(', ')}`)
    }

    // 使用authService进行登录
    await authService.loginWithGoogle()

  } catch (error) {
    console.error('Google 登录失败:', error)
    ElMessage.error(error instanceof Error ? error.message : t('login.loginFailed'))
    loading.value = false
  }
}

// Facebook 登录处理
const handleFacebookLogin = async () => {
  try {
    if (deviceInfo.value.isInApp) {
      ElMessage.warning(t('login.recommendSystemBrowser'))
    }

    loading.value = true
    console.log('移动端Facebook登录开始...')

    await authService.loginWithFacebook()

  } catch (error) {
    console.error('Facebook 登录失败:', error)
    ElMessage.error(error instanceof Error ? error.message : t('login.loginFailed'))
    loading.value = false
  }
}

// 跳转到登录指南
const goToGuide = () => {
  router.push('/mobile/auth/guide')
}

// 复制页面URL
const copyPageUrl = async () => {
  try {
    const url = window.location.href
    await navigator.clipboard.writeText(url)
    ElMessage.success(t('login.pageLinkCopied'))
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = window.location.href
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success(t('login.pageLinkCopied'))
  }
}

// 组件挂载
onMounted(() => {
  // 不再重复初始化用户状态，路由守卫已经处理了
  // 如果已经登录，直接跳转
  if (userStore.isLoggedIn) {
    console.log('用户已登录，跳转到产品页面')
    router.push('/products')
  }

  console.log('移动端登录页面加载完成')
  console.log('设备信息:', deviceInfo.value)
})
</script>

<style scoped>
.mobile-login-container {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.mobile-login-container .page-container {
  width: 100%;
  max-width: none !important;
  margin: 0 !important;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 15px;
  box-sizing: border-box;
}

.language-switcher-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.login-content {
  width: 100%;
  max-width: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-card {
  width: 100%;
  padding: 30px 25px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-title {
  text-align: center;
  margin-bottom: 8px;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
}

.login-subtitle {
  text-align: center;
  margin-bottom: 25px;
  color: #666;
  font-size: 0.95rem;
}



.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.mobile-login-btn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.mobile-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-login-btn:disabled {
  opacity: 0.6;
  transform: none;
}

.google-login-btn {
  background: white;
  border: 1px solid #dadce0;
  color: #3c4043;
}

.facebook-login-btn {
  background: #1877f2;
  border: 1px solid #1877f2;
  color: white;
}

.login-icon {
  width: 20px;
  height: 20px;
}

.facebook-icon {
  filter: brightness(0) invert(1);
}

.in-app-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.warning-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.warning-text p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #856404;
}

.warning-text p:first-child {
  font-weight: 600;
  margin-bottom: 4px;
}

.warning-actions {
  display: flex;
  gap: 10px;
}



/* RTL支持 */
[dir="rtl"] .language-switcher-container {
  right: auto;
  left: 20px;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .mobile-login-container .page-container {
    padding: 15px 10px;
  }

  .login-card {
    padding: 25px 20px;
    margin: 0 5px;
  }

  .login-title {
    font-size: 1.6rem;
  }

  .mobile-login-btn {
    height: 48px;
    font-size: 0.95rem;
  }
}
</style>
