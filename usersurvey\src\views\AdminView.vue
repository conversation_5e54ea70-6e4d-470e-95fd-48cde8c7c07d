<template>
  <div class="admin-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>管理员面板</span>
          <el-button type="danger" @click="handleLogout">退出登录</el-button>
        </div>
      </template>
      
      <div class="admin-content">
        <el-alert
          title="欢迎使用管理员面板"
          type="success"
          :closable="false"
          show-icon
        />
        
        <div class="user-info">
          <h3>当前用户信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">{{ adminStore.adminInfo?.id }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ adminStore.adminInfo?.userName }}</el-descriptions-item>
            <el-descriptions-item label="角色">{{ adminStore.adminInfo?.role }}</el-descriptions-item>
            <el-descriptions-item label="登录状态">
              <el-tag type="success">已登录</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="survey-status">
          <h3>调查状态控制</h3>
          <el-card shadow="hover">
            <div class="status-control">
              <div class="status-info">
                <el-icon size="40" :color="surveyStatus.isOpen ? '#67C23A' : '#F56C6C'">
                  <component :is="surveyStatus.isOpen ? 'CircleCheck' : 'CircleClose'" />
                </el-icon>
                <div class="status-text">
                  <h4>调查状态: {{ surveyStatus.isOpen ? '开放中' : '已关闭' }}</h4>
                  <p>最后更新: {{ formatTime(surveyStatus.lastUpdateTime) }}</p>
                </div>
              </div>
              <div class="status-actions">
                <el-button
                  :type="surveyStatus.isOpen ? 'danger' : 'success'"
                  :loading="statusLoading"
                  @click="toggleSurveyStatus"
                >
                  <el-icon>
                    <component :is="surveyStatus.isOpen ? 'CircleClose' : 'CircleCheck'" />
                  </el-icon>
                  {{ surveyStatus.isOpen ? '关闭调查' : '开启调查' }}
                </el-button>
              </div>
            </div>
          </el-card>
        </div>

        <div class="admin-actions">
          <h3>管理功能</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="action-item">
                  <el-icon size="40" color="#409EFF"><User /></el-icon>
                  <h4>用户管理</h4>
                  <p>管理系统用户</p>
                  <el-button type="primary" @click="goToUserManagement">
                    <el-icon><User /></el-icon>
                    用户管理
                  </el-button>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="action-item">
                  <el-icon size="40" color="#67C23A"><Goods /></el-icon>
                  <h4>产品管理</h4>
                  <p>管理产品信息</p>
                  <div class="action-buttons">
                    <el-button type="success" @click="goToAddProduct">
                      <el-icon><Plus /></el-icon>
                      添加产品
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="action-item">
                  <el-icon size="40" color="#E6A23C"><DataAnalysis /></el-icon>
                  <h4>调研统计</h4>
                  <p>查看调研结果</p>
                  <el-button type="warning" @click="goToStatistics">
                    <el-icon><DataAnalysis /></el-icon>
                    查看统计
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAdminStore } from '@/stores/admin'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Goods, DataAnalysis, Plus, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import axios from 'axios'

const adminStore = useAdminStore()
const router = useRouter()

// 调查状态相关
const surveyStatus = ref({
  isOpen: true,
  lastUpdateTime: new Date()
})
const statusLoading = ref(false)

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    adminStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消操作
  }
}

// 跳转到产品添加页面
const goToAddProduct = () => {
  router.push('/admin/add-product')
}

// 跳转到统计页面
const goToStatistics = () => {
  router.push('/admin/statistics')
}

// 跳转到用户管理页面
const goToUserManagement = () => {
  router.push('/admin/users')
}

// 获取调查状态
const getSurveyStatus = async () => {
  try {
    const response = await axios.get('/api/SurveyStatus/GetStatus')
    if (response.data.success) {
      surveyStatus.value = {
        isOpen: response.data.data.isOpen,
        lastUpdateTime: new Date(response.data.data.lastUpdateTime)
      }
    }
  } catch (error) {
    console.error('获取调查状态失败:', error)
    ElMessage.error('获取调查状态失败')
  }
}

// 切换调查状态
const toggleSurveyStatus = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要${surveyStatus.value.isOpen ? '关闭' : '开启'}调查吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    statusLoading.value = true
    const response = await axios.post('/api/SurveyStatus/UpdateStatus', {
      isOpen: !surveyStatus.value.isOpen
    })

    if (response.data.success) {
      surveyStatus.value = {
        isOpen: response.data.data.isOpen,
        lastUpdateTime: new Date(response.data.data.lastUpdateTime)
      }
      ElMessage.success(response.data.message)
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('更新调查状态失败:', error)
      ElMessage.error('更新调查状态失败')
    }
  } finally {
    statusLoading.value = false
  }
}

// 格式化时间
const formatTime = (time: Date) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 页面加载时获取调查状态
onMounted(() => {
  getSurveyStatus()
})
</script>

<style scoped>
.admin-container {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 60px);
  background: #f5f5f5;
}

:deep(.el-card) {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

:deep(.el-card:hover) {
  transform: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 400;
  color: #333;
}

.admin-content {
  padding: 20px 0;
}

:deep(.el-alert) {
  border-radius: 4px;
  border: 1px solid #67c23a;
}

.user-info {
  margin: 30px 0;
}

.survey-status {
  margin: 30px 0;
}

.status-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-text h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.status-text p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.status-actions {
  display: flex;
  gap: 10px;
}

:deep(.el-descriptions) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: none;
  border: 1px solid #e0e0e0;
}

.admin-actions {
  margin-top: 30px;
}

:deep(.el-card.is-hover-shadow) {
  background: white;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid #e0e0e0;
}

:deep(.el-card.is-hover-shadow:hover) {
  transform: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-item {
  text-align: center;
  padding: 20px 15px;
}

.action-item h4 {
  margin: 15px 0 10px 0;
  color: #333;
  font-size: 16px;
  font-weight: 400;
}

.action-item p {
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.6;
}

:deep(.el-button) {
  border-radius: 4px;
  font-weight: 600;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  border: none;
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 101, 101, 0.4);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.action-buttons .el-button {
  width: 140px;
  padding: 12px 20px;
}

h3 {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 20px;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-container {
    padding: 20px 15px;
  }

  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  :deep(.el-col) {
    margin-bottom: 20px;
  }

  .action-item {
    padding: 25px 15px;
  }
}

@media (max-width: 480px) {
  .admin-container {
    padding: 15px 10px;
  }

  .action-item h4 {
    font-size: 16px;
  }

  .action-item p {
    font-size: 13px;
  }
}
</style>
