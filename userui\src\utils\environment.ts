/**
 * 环境工具函数
 * 提供环境检测和配置功能
 */

export interface EnvironmentConfig {
  // 环境标识
  env: 'development' | 'production'
  isDev: boolean
  isProd: boolean

  // 协议配置
  isHttps: boolean
  forceHttps: boolean
  sslEnabled: boolean

  // API 配置
  apiBaseUrl: string
  apiHost: string
  apiPort: number
  apiProtocol: string
  apiTimeout: number

  // 前端配置
  frontendBaseUrl: string
  frontendHost: string
  frontendPort: number
  frontendProtocol: string

  // OAuth 配置 (仅前端公开信息)
  google: {
    clientId: string
    authUrl: string
    scope: string
  }

  facebook: {
    appId: string
    authUrl: string
    scope: string
  }

  // OAuth 回调配置
  oauthCallbackBaseUrl: string

  // 调试配置
  debugMode: boolean
  logLevel: string
  showConsoleLogs: boolean

  // 其他配置
  corsEnabled: boolean
  corsOrigin: string
  cacheEnabled: boolean
  cacheDuration: number
}

// 获取环境配置
export const getEnvironmentConfig = (): EnvironmentConfig => {
  // 环境检测
  const env = (import.meta.env.VITE_ENV || import.meta.env.MODE || 'development') as 'development' | 'production'
  const isDev = env === 'development'
  const isProd = env === 'production'

  // 协议配置
  const forceHttps = import.meta.env.VITE_FORCE_HTTPS === 'true'
  const sslEnabled = import.meta.env.VITE_SSL_ENABLED === 'true'
  const isHttps = typeof window !== 'undefined' ? window.location.protocol === 'https:' : (forceHttps || sslEnabled)

  // API 配置 - 自动适配协议
  const getApiConfig = () => {
    if (isDev) {
      // 开发环境使用代理
      return {
        baseUrl: '',
        protocol: 'http',
        host: 'localhost',
        port: 8080
      }
    }

    // 生产环境：自动适配当前页面协议
    const currentProtocol = typeof window !== 'undefined' ? window.location.protocol.replace(':', '') : 'http'
    const host = import.meta.env.VITE_API_HOST || 'localhost'

    // 根据当前页面协议选择对应的后端端口
    const port = currentProtocol === 'https' ? 8081 : 8080
    const baseUrl = `${currentProtocol}://${host}:${port}`

    return {
      baseUrl,
      protocol: currentProtocol,
      host,
      port
    }
  }

  const apiConfig = getApiConfig()
  const apiBaseUrl = apiConfig.baseUrl
  const apiProtocol = apiConfig.protocol
  const apiHost = apiConfig.host
  const apiPort = apiConfig.port
  const apiTimeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000')

  // 前端配置
  const frontendProtocol = import.meta.env.VITE_APP_PROTOCOL || (isDev ? 'http' : 'https')
  const frontendHost = import.meta.env.VITE_APP_HOST || 'localhost'
  const frontendPort = parseInt(import.meta.env.VITE_APP_PORT || '3000')
  const frontendBaseUrl = import.meta.env.VITE_APP_BASE_URL ||
    (typeof window !== 'undefined' ? window.location.origin : `${frontendProtocol}://${frontendHost}:${frontendPort}`)

  return {
    // 环境标识
    env,
    isDev,
    isProd,

    // 协议配置
    isHttps,
    forceHttps,
    sslEnabled,

    // API 配置
    apiBaseUrl,
    apiHost,
    apiPort,
    apiProtocol,
    apiTimeout,

    // 前端配置
    frontendBaseUrl,
    frontendHost,
    frontendPort,
    frontendProtocol,

    // Google OAuth 配置 (仅公开信息)
    google: {
      clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
      authUrl: import.meta.env.VITE_GOOGLE_AUTH_URL || 'https://accounts.google.com/o/oauth2/v2/auth',
      scope: import.meta.env.VITE_GOOGLE_SCOPE || 'openid email profile'
    },

    // Facebook OAuth 配置 (仅公开信息)
    facebook: {
      appId: import.meta.env.VITE_FACEBOOK_APP_ID || '',
      authUrl: import.meta.env.VITE_FACEBOOK_AUTH_URL || 'https://www.facebook.com/v18.0/dialog/oauth',
      scope: import.meta.env.VITE_FACEBOOK_SCOPE || 'email,public_profile'
    },

    // OAuth 回调配置 - 自动适配协议
    oauthCallbackBaseUrl: (() => {
      if (isDev) {
        // 开发环境：根据 VITE_USE_HTTPS 选择
        const useHttps = import.meta.env.VITE_USE_HTTPS === 'true'
        return useHttps ? 'https://localhost:8081/api/auth' : 'http://localhost:8080/api/auth'
      } else {
        // 生产环境：自动适配
        return `${apiConfig.protocol}://${apiConfig.host}:${apiConfig.port}/api/auth`
      }
    })(),

    // 调试配置
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
    logLevel: import.meta.env.VITE_LOG_LEVEL || 'info',
    showConsoleLogs: import.meta.env.VITE_SHOW_CONSOLE_LOGS === 'true',

    // 其他配置
    corsEnabled: import.meta.env.VITE_CORS_ENABLED === 'true',
    corsOrigin: import.meta.env.VITE_CORS_ORIGIN || frontendBaseUrl,
    cacheEnabled: import.meta.env.VITE_CACHE_ENABLED === 'true',
    cacheDuration: parseInt(import.meta.env.VITE_CACHE_DURATION || '300000')
  }
}

// 获取OAuth重定向URI
export const getOAuthRedirectUri = (provider: 'google' | 'facebook'): string => {
  const config = getEnvironmentConfig()
  
  // 优先使用环境变量配置
  const envKey = `VITE_${provider.toUpperCase()}_REDIRECT_URI`
  const envUri = import.meta.env[envKey]
  
  if (envUri) {
    return envUri
  }
  
  // 根据环境自动生成
  return `${config.frontendBaseUrl}/auth/${provider}/callback`
}

// 检查环境配置是否有效
export const validateEnvironmentConfig = () => {
  const config = getEnvironmentConfig()
  const errors: string[] = []
  const warnings: string[] = []
  
  // 检查API URL
  if (!config.apiBaseUrl) {
    errors.push('API基础URL未配置')
  }
  
  // 检查协议一致性
  if (config.isHttps && config.apiBaseUrl.startsWith('http://')) {
    warnings.push('前端使用HTTPS但API使用HTTP，可能导致混合内容问题')
  }
  
  // 检查端口配置
  if (config.isDev) {
    if (!config.apiBaseUrl.includes('localhost')) {
      warnings.push('开发环境建议使用localhost')
    }
  }
  
  return {
    isValid: errors.length === 0,
    hasWarnings: warnings.length > 0,
    errors,
    warnings,
    config
  }
}

// 环境信息日志
export const logEnvironmentInfo = () => {
  const config = getEnvironmentConfig()
  const validation = validateEnvironmentConfig()
  
  console.group('🌍 Environment Configuration')
  console.log('Development mode:', config.isDev)
  console.log('Production mode:', config.isProd)
  console.log('HTTPS mode:', config.isHttps)
  console.log('Force HTTPS:', config.forceHttps)
  console.log('API URL:', config.apiBaseUrl)
  console.log('Frontend URL:', config.frontendBaseUrl)

  if (validation.hasWarnings) {
    console.warn('⚠️ Configuration warnings:', validation.warnings)
  }

  if (!validation.isValid) {
    console.error('❌ Configuration errors:', validation.errors)
  }
  
  console.groupEnd()
  
  return config
}

// 切换到生产环境配置
export const switchToProduction = () => {
  console.log('🚀 Switching to production environment configuration')

  // Add switching logic here, such as updating localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem('force_production', 'true')
    window.location.reload()
  }
}

// 切换到开发环境配置
export const switchToDevelopment = () => {
  console.log('🛠️ Switching to development environment configuration')

  if (typeof window !== 'undefined') {
    localStorage.removeItem('force_production')
    window.location.reload()
  }
}

// 导出环境配置实例
export const ENV_CONFIG = getEnvironmentConfig()

// 在开发模式下输出环境信息
if (ENV_CONFIG.isDev) {
  logEnvironmentInfo()
}
