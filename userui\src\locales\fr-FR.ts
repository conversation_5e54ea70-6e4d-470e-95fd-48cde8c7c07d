export default {
  // Commun
  common: {
    submit: 'Soumettre',
    skip: 'Ignorer',
    next: 'Groupe Suivant',
    previous: 'Groupe Précédent',
    loading: 'Chargement...',
    logout: 'Se Déconnecter',
    cancel: 'Annuler',
    confirm: 'Confirmer'
  },
  
  // Page de connexion
  login: {
    title: 'Système de Recherche Utilisateur',
    subtitle: 'Veuillez sélectionner la méthode de connexion',
    googleLogin: 'Se connecter avec Google',
    facebookLogin: 'Se connecter avec Facebook',
    loginSuccess: 'Connexion réussie',
    loginFailed: 'Échec de la connexion, veuillez réessayer',
    loggingIn: 'Connexion en cours...',
    facebookNotConfigured: 'Facebook Non Configuré',
    facebookConfigNotice: 'Connexion Facebook Non Configurée',
    facebookConfigDesc: 'La connexion Facebook nécessite une configuration d\'App ID pour fonctionner.',
    facebookConfigGuide: 'Veuillez consulter FACEBOOK_SETUP_GUIDE.md pour la configuration.',
    inAppBrowserWarning: 'Navigateur intégré détecté, il est recommandé de copier le lien vers le navigateur système pour une meilleure expérience de connexion',
    configError: 'Erreur de Configuration',
    facebookNotConfiguredError: 'Connexion Facebook non configurée, veuillez contacter l\'administrateur pour configurer Facebook App ID',
    inAppBrowserDetected: 'Navigateur Intégré Détecté',
    inAppBrowserDesc: 'Pour une connexion réussie, il est recommandé d\'ouvrir dans le navigateur système',
    viewLoginGuide: 'Voir le Guide de Connexion',
    copyPageLink: 'Copier le Lien de la Page',
    pageLinkCopied: 'Lien de la page copié dans le presse-papiers',
    recommendSystemBrowser: 'Il est recommandé de se connecter avec le navigateur système pour une meilleure expérience'
  },
  
  // Page de sélection des produits
  products: {
    title: 'Sélection de Produits',
    groupInfo: 'Groupe {current} / {total}',
    productCount: '({count} produits)',
    selectedCount: 'Sélectionnés {selected} / {max} produits',
    selectionHint: 'Vous pouvez sélectionner jusqu\'à {max} produits par groupe',
    noData: 'Aucune donnée de produit disponible',
    selectAtLeastOne: 'Veuillez sélectionner au moins un produit dans un groupe',
    selectProductFirst: 'Veuillez sélectionner des produits avant d\'ignorer',
    logoutSuccess: 'Déconnexion réussie',
    dislikeAll: 'Je n\'aime aucun',
    dislikeAllTitle: 'Confirmer l\'action',
    dislikeAllConfirm: 'Êtes-vous sûr de ne pas aimer tous les produits?',
    tooManySelected: 'Vous pouvez sélectionner au maximum {max} produits',
    noProductSelected: 'Aucun produit sélectionné actuellement'
  },
  
  // Page d'informations utilisateur
  userInfo: {
    title: 'Compléter le Profil',
    subtitle: 'Veuillez remplir vos informations de base pour nous aider à mieux comprendre vos besoins',
    name: 'Nom',
    namePlaceholder: 'Veuillez entrer votre nom',
    nameRequired: 'Veuillez entrer votre nom',
    gender: 'Genre',
    genderRequired: 'Veuillez sélectionner le genre',
    male: 'Masculin',
    female: 'Féminin',
    birthday: 'Anniversaire',
    birthdayPlaceholder: 'Veuillez sélectionner votre anniversaire',
    country: 'Pays',
    countryPlaceholder: 'Veuillez entrer votre pays',
    address: 'Adresse',
    addressPlaceholder: 'Veuillez entrer votre adresse détaillée',
    selectedProducts: 'Vos Produits Sélectionnés',
    totalSelected: 'Total {count} produits sélectionnés',
    groupLabel: 'Groupe {index}:',
    submitSuccess: 'Soumis avec succès! Merci pour votre aide, nous vous enverrons un petit cadeau en remerciement!',
    skipSuccess: 'Données de vote sauvegardées, merci pour votre participation!',
    submitFailed: 'Échec de la soumission, veuillez réessayer',
    skipFailed: 'Échec de l\'ignorance, veuillez réessayer'
  },
  
  // Page de fin
  end: {
    title: 'Soumission Réussie!',
    message: 'Merci de participer à notre recherche de produits!<br>Vos précieux commentaires nous aideront à fournir de meilleurs produits et services.',
    giftInfo: 'Nous vous enverrons un petit cadeau en remerciement!'
  },
  
  // Page de service indisponible
  serviceUnavailable: {
    title: 'Site web temporairement inaccessible',
    description: 'Nous nous excusons, le système de recherche est temporairement fermé et ne peut pas fournir de service.',
    subDescription: 'Le système détectera automatiquement l\'état du service et redirigera vers la page de connexion une fois restauré. Cliquez sur le bouton actualiser pour vérifier l\'état du système.',
    refresh: 'Actualiser la page',
    contact: 'Si vous avez des questions, veuillez contacter l\'administrateur système',
    systemRestored: 'Le système de recherche a été restauré, redirection en cours...',
    systemStillClosed: 'Le système de recherche est toujours fermé, veuillez réessayer plus tard',
    checkFailed: 'Échec de la vérification de l\'état, veuillez réessayer plus tard'
  },

  // Messages d'erreur
  errors: {
    networkError: 'Erreur réseau, veuillez vérifier votre connexion',
    serverError: 'Erreur serveur, veuillez réessayer plus tard',
    unknownError: 'Erreur inconnue, veuillez réessayer'
  },

  // Page de callback d'authentification
  auth: {
    processing: 'Traitement des Informations de Connexion',
    processingDesc: 'Veuillez patienter, nous vérifions votre identité...',
    loginSuccess: 'Connexion réussie ! Redirection en cours...',
    loginFailed: 'Échec de la connexion, veuillez réessayer',
    networkError: 'Échec de la connexion réseau, veuillez vérifier le réseau et réessayer',
    serverError: 'Serveur temporairement indisponible, veuillez réessayer plus tard',
    authError: 'Échec de l\'autorisation, veuillez vous reconnecter',
    noAuthCode: 'Aucun code d\'autorisation reçu',
    securityError: 'Échec de la vérification de sécurité, veuillez vous reconnecter',
    backendError: 'Erreur de réponse du backend',
    userCancelled: 'Autorisation annulée, retour à la page de connexion',
    noAuthCode: 'Aucun code d\'autorisation reçu',
    securityError: 'Échec de la vérification de sécurité, veuillez vous reconnecter',
    loginSuccess: 'Connexion réussie ! Redirection...',
    loginFailed: 'Échec de la connexion',
    networkError: 'Échec de la connexion réseau, veuillez vérifier le réseau et réessayer',
    serverError: 'Serveur temporairement indisponible, veuillez réessayer plus tard',
    authError: 'Échec de l\'autorisation, veuillez vous reconnecter'
  },

  // Page de guide de connexion mobile
  mobileGuide: {
    title: '📱 Guide de Connexion Mobile',
    subtitle: 'Pour obtenir la meilleure expérience de connexion, veuillez suivre ces étapes',
    environmentDetection: '🔍 Détection de l\'Environnement Actuel',
    deviceType: 'Type d\'Appareil:',
    mobileDevice: 'Appareil Mobile',
    desktopDevice: 'Appareil de Bureau',
    browserEnvironment: 'Environnement du Navigateur:',
    recommendedMethod: 'Méthode Recommandée:',
    systemBrowser: 'Navigateur Système',
    wechatBrowser: 'Navigateur Intégré WeChat',
    qqBrowser: 'Navigateur Intégré QQ',
    alipayBrowser: 'Navigateur Intégré Alipay',
    inAppBrowser: 'Navigateur Dans l\'App',
    directLogin: 'Connexion Directe',
    copyToSystemBrowser: 'Copier le lien vers le navigateur système',
    mobileOptimizedLogin: 'Connexion optimisée mobile',
    inAppBrowserGuide: '⚠️ Guide de Connexion Navigateur Dans l\'App',
    inAppWarning: 'Détecté que vous utilisez un navigateur dans l\'application (comme WeChat, QQ, etc.), ce qui peut affecter la fonctionnalité de connexion tierce.',
    step1Title: 'Copier le lien de la page actuelle',
    step1Desc: 'Cliquez sur le bouton ci-dessous pour copier le lien de la page',
    copyLink: '📋 Copier le Lien',
    step2Title: 'Ouvrir dans le navigateur système',
    step2Desc: 'Ouvrez le navigateur système de votre téléphone (Safari, Chrome, etc.), collez le lien et visitez',
    step3Title: 'Terminer la connexion',
    step3Desc: 'Cliquez sur le bouton de connexion Google dans le navigateur système pour terminer l\'authentification',
    mobileTips: '📱 Conseils de Connexion Mobile',
    tip1: 'Assurez-vous d\'avoir une connexion réseau stable',
    tip2: 'Autoriser les pop-ups du navigateur (si nécessaire)',
    tip3: 'Ne changez pas d\'application pendant la connexion',
    openedInSystemBrowser: 'J\'ai ouvert dans le navigateur système',
    startLogin: 'Commencer la Connexion',
    confirmed: '✅ Confirmé',
    confirmCopied: 'Confirmer le lien copié',
    backToHome: 'Retour à l\'Accueil',
    linkCopied: 'Lien copié dans le presse-papiers',
    pleaseConfirmFirst: 'Veuillez d\'abord copier le lien et l\'ouvrir dans le navigateur système'
  }
}
