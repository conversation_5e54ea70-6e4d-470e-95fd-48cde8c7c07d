<template>
  <div class="mobile-auth-guide">
    <div class="guide-container">
      <div class="guide-header">
        <h1>{{ $t('mobileGuide.title') }}</h1>
        <p class="subtitle">{{ $t('mobileGuide.subtitle') }}</p>
      </div>

      <div class="guide-content">
        <!-- 检测到的环境信息 -->
        <div class="environment-info">
          <h3>{{ $t('mobileGuide.environmentDetection') }}</h3>
          <div class="info-item">
            <span class="label">{{ $t('mobileGuide.deviceType') }}</span>
            <span class="value">{{ deviceInfo.isMobile ? $t('mobileGuide.mobileDevice') : $t('mobileGuide.desktopDevice') }}</span>
          </div>
          <div class="info-item">
            <span class="label">{{ $t('mobileGuide.browserEnvironment') }}</span>
            <span class="value">{{ deviceInfo.browserType }}</span>
          </div>
          <div class="info-item">
            <span class="label">{{ $t('mobileGuide.recommendedMethod') }}</span>
            <span class="value">{{ deviceInfo.recommendedMethod }}</span>
          </div>
        </div>

        <!-- 登录步骤指导 -->
        <div class="login-steps" v-if="deviceInfo.isInApp">
          <h3>{{ $t('mobileGuide.inAppBrowserGuide') }}</h3>
          <div class="warning-box">
            <p>{{ $t('mobileGuide.inAppWarning') }}</p>
          </div>
          
          <div class="steps">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>{{ $t('mobileGuide.step1Title') }}</h4>
                <p>{{ $t('mobileGuide.step1Desc') }}</p>
                <el-button @click="copyCurrentUrl" type="primary" size="small">
                  {{ $t('mobileGuide.copyLink') }}
                </el-button>
              </div>
            </div>
            
            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>{{ $t('mobileGuide.step2Title') }}</h4>
                <p>{{ $t('mobileGuide.step2Desc') }}</p>
              </div>
            </div>
            
            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>{{ $t('mobileGuide.step3Title') }}</h4>
                <p>{{ $t('mobileGuide.step3Desc') }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 普通移动端指导 -->
        <div class="mobile-tips" v-else-if="deviceInfo.isMobile">
          <h3>{{ $t('mobileGuide.mobileTips') }}</h3>
          <div class="tips-list">
            <div class="tip">
              <span class="tip-icon">✅</span>
              <span>{{ $t('mobileGuide.tip1') }}</span>
            </div>
            <div class="tip">
              <span class="tip-icon">✅</span>
              <span>{{ $t('mobileGuide.tip2') }}</span>
            </div>
            <div class="tip">
              <span class="tip-icon">✅</span>
              <span>{{ $t('mobileGuide.tip3') }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button 
            type="primary" 
            size="large" 
            @click="proceedToLogin"
            :disabled="deviceInfo.isInApp && !hasConfirmed"
          >
            {{ deviceInfo.isInApp ? $t('mobileGuide.openedInSystemBrowser') : $t('mobileGuide.startLogin') }}
          </el-button>
          
          <el-button 
            v-if="deviceInfo.isInApp"
            @click="hasConfirmed = true"
            size="large"
            :type="hasConfirmed ? 'success' : 'default'"
          >
            {{ hasConfirmed ? $t('mobileGuide.confirmed') : $t('mobileGuide.confirmCopied') }}
          </el-button>
          
          <el-button @click="goBack" size="large">
            {{ $t('mobileGuide.backToHome') }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { isMobile, isInAppBrowser } from '@/config/auth'

const router = useRouter()
const { t } = useI18n()
const hasConfirmed = ref(false)

// 设备信息
const deviceInfo = computed(() => {
  const mobile = isMobile()
  const inApp = isInAppBrowser()
  
  let browserType = t('mobileGuide.systemBrowser')
  if (inApp) {
    const ua = navigator.userAgent.toLowerCase()
    if (ua.includes('micromessenger')) browserType = t('mobileGuide.wechatBrowser')
    else if (ua.includes('qq')) browserType = t('mobileGuide.qqBrowser')
    else if (ua.includes('alipay')) browserType = t('mobileGuide.alipayBrowser')
    else browserType = t('mobileGuide.inAppBrowser')
  }

  let recommendedMethod = t('mobileGuide.directLogin')
  if (inApp) recommendedMethod = t('mobileGuide.copyToSystemBrowser')
  else if (mobile) recommendedMethod = t('mobileGuide.mobileOptimizedLogin')
  
  return {
    isMobile: mobile,
    isInApp: inApp,
    browserType,
    recommendedMethod
  }
})

// 复制当前URL
const copyCurrentUrl = async () => {
  try {
    const url = window.location.origin + '/login'
    await navigator.clipboard.writeText(url)
    ElMessage.success(t('mobileGuide.linkCopied'))
    hasConfirmed.value = true
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = window.location.origin + '/login'
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success(t('mobileGuide.linkCopied'))
    hasConfirmed.value = true
  }
}

// 继续登录
const proceedToLogin = () => {
  if (deviceInfo.value.isInApp && !hasConfirmed.value) {
    ElMessage.warning(t('mobileGuide.pleaseConfirmFirst'))
    return
  }
  router.push('/login')
}

// 返回首页
const goBack = () => {
  router.push('/')
}

onMounted(() => {
  console.log('Mobile login guide page loaded')
  console.log('Device info:', deviceInfo.value)
})
</script>

<style scoped>
.mobile-auth-guide {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.guide-container {
  max-width: 500px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.guide-header {
  text-align: center;
  margin-bottom: 30px;
}

.guide-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

.subtitle {
  color: #666;
  font-size: 0.9rem;
}

.environment-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
}

.environment-info h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.label {
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}

.warning-box {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.warning-box p {
  color: #856404;
  margin: 0;
  font-size: 0.9rem;
}

.steps {
  margin-top: 20px;
}

.step {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.step-content h4 {
  color: #333;
  margin-bottom: 5px;
  font-size: 1rem;
}

.step-content p {
  color: #666;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.tips-list {
  margin-top: 15px;
}

.tip {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.9rem;
}

.tip-icon {
  margin-right: 10px;
  font-size: 1.1rem;
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-buttons .el-button {
  width: 100%;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .mobile-auth-guide {
    padding: 15px 10px;
  }

  .guide-container {
    padding: 20px;
    margin: 0 5px;
  }

  .guide-header h1 {
    font-size: 1.5rem;
  }
}
</style>
