@import './base.css';
@import './rtl.css';

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
  font-weight: normal;
}

/* 响应式容器 */
.page-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-container {
    padding: 15px;
  }
}

/* 平板适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .page-container {
    padding: 25px;
    max-width: 900px;
  }
}

/* 桌面端适配 */
@media (min-width: 1025px) {
  .page-container {
    padding: 30px;
    max-width: 1200px;
  }
}

/* 超大屏幕适配 */
@media (min-width: 1440px) {
  .page-container {
    max-width: 1400px;
  }
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 语言切换器下拉菜单全局样式 */
.el-dropdown-menu {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

.el-dropdown-menu__item {
  transition: all 0.2s ease !important;
}

.el-dropdown-menu__item:hover {
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #409eff !important;
}
