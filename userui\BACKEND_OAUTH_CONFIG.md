# 后端OAuth配置说明

## 🔒 安全重构说明

为了提高安全性，我们已将OAuth的敏感信息（Client Secret、App Secret）从前端移到后端。现在需要在后端配置这些敏感信息。

## 📋 后端需要配置的环境变量

### appsettings.json 或环境变量配置

```json
{
  "OAuth": {
    "Google": {
      "ClientId": "************-su2s5e82v75beq482m3dbb964rstp8ud.apps.googleusercontent.com",
      "ClientSecret": "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",
      "RedirectUri": "http://localhost:8080/api/auth/google/callback",
      "AuthUrl": "https://accounts.google.com/o/oauth2/v2/auth",
      "TokenUrl": "https://oauth2.googleapis.com/token",
      "UserInfoUrl": "https://www.googleapis.com/oauth2/v2/userinfo",
      "Scope": "openid email profile"
    },
    "Facebook": {
      "AppId": "****************",
      "AppSecret": "93ebca2fdb3e81c7a0486aab4020fb19",
      "RedirectUri": "http://localhost:8080/api/auth/facebook/callback",
      "AuthUrl": "https://www.facebook.com/v18.0/dialog/oauth",
      "TokenUrl": "https://graph.facebook.com/v18.0/oauth/access_token",
      "UserInfoUrl": "https://graph.facebook.com/v18.0/me",
      "Scope": "email,public_profile"
    }
  },
  "Frontend": {
    "BaseUrl": "http://localhost:3000",
    "SuccessRedirectUrl": "http://localhost:3000/auth/success"
  }
}
```

### 生产环境配置

```json
{
  "OAuth": {
    "Google": {
      "ClientId": "************-su2s5e82v75beq482m3dbb964rstp8ud.apps.googleusercontent.com",
      "ClientSecret": "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",
      "RedirectUri": "https://localhost:8081/api/auth/google/callback",
      "AuthUrl": "https://accounts.google.com/o/oauth2/v2/auth",
      "TokenUrl": "https://oauth2.googleapis.com/token",
      "UserInfoUrl": "https://www.googleapis.com/oauth2/v2/userinfo",
      "Scope": "openid email profile"
    },
    "Facebook": {
      "AppId": "****************",
      "AppSecret": "93ebca2fdb3e81c7a0486aab4020fb19",
      "RedirectUri": "https://localhost:8081/api/auth/facebook/callback",
      "AuthUrl": "https://www.facebook.com/v18.0/dialog/oauth",
      "TokenUrl": "https://graph.facebook.com/v18.0/oauth/access_token",
      "UserInfoUrl": "https://graph.facebook.com/v18.0/me",
      "Scope": "email,public_profile"
    }
  },
  "Frontend": {
    "BaseUrl": "https://localhost:3000",
    "SuccessRedirectUrl": "https://localhost:3000/auth/success"
  }
}
```

## 🔄 新的OAuth流程

### 1. 前端发起登录
- 用户点击登录按钮
- 前端生成state参数并保存
- 跳转到OAuth提供商授权页面
- **回调地址指向后端**: `/api/auth/{provider}/callback`

### 2. 后端处理回调
- 接收OAuth授权码
- 使用Client Secret换取访问令牌
- 获取用户信息
- 生成JWT token
- 重定向到前端成功页面: `/auth/success?token={jwt_token}`

### 3. 前端处理结果
- 从URL参数获取token
- 保存token到localStorage
- 获取用户信息
- 跳转到相应页面

## 🛠️ 需要实现的后端API端点

### 1. OAuth回调处理
```
POST /api/auth/google/callback
POST /api/auth/facebook/callback
```

### 2. 认证状态检查
```
GET /api/auth/status
Authorization: Bearer {token}
```

### 3. 响应格式

#### 成功回调重定向
```
HTTP 302 Redirect
Location: http://localhost:3000/auth/success?token={jwt_token}&state={state}
```

#### 失败回调重定向
```
HTTP 302 Redirect
Location: http://localhost:3000/auth/success?error={error_message}&state={state}
```

#### 状态检查响应
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "avatar": "avatar_url",
    "provider": "google",
    "hasSubmitted": false
  }
}
```

## 🔐 安全考虑

1. **敏感信息隔离**: Client Secret和App Secret只在后端存储
2. **CSRF保护**: 使用state参数防止CSRF攻击
3. **Token安全**: 使用JWT token进行前后端通信
4. **HTTPS强制**: 生产环境强制使用HTTPS
5. **回调验证**: 验证回调来源和参数

## 📝 Google Cloud Console配置

⚠️ **重要**: 需要在Google Cloud Console中更新重定向URI配置

### 当前配置 (需要更新)
旧的重定向URI (前端回调):
- ❌ `http://localhost:3000/auth/google/callback`
- ❌ `http://localhost:5173/auth/google/callback`

### 新配置 (后端回调)
需要添加的新重定向URI:
- ✅ 开发环境: `http://localhost:8080/api/auth/google/callback`
- ✅ 生产环境: `https://localhost:8081/api/auth/google/callback`

### 配置步骤
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择您的项目
3. 转到 "APIs & Services" > "Credentials"
4. 找到您的OAuth 2.0客户端ID
5. 点击编辑
6. 在"授权重定向URI"部分:
   - 删除旧的前端回调URI
   - 添加新的后端回调URI
7. 保存更改

## 📝 Facebook开发者控制台配置

⚠️ **重要**: 需要在Facebook开发者控制台中更新重定向URI配置

### 当前配置 (需要更新)
旧的重定向URI (前端回调):
- ❌ `http://localhost:3000/auth/facebook/callback`
- ❌ `http://localhost:5173/auth/facebook/callback`

### 新配置 (后端回调)
需要添加的新重定向URI:
- ✅ 开发环境: `http://localhost:8080/api/auth/facebook/callback`
- ✅ 生产环境: `https://localhost:8081/api/auth/facebook/callback`

### 配置步骤
1. 访问 [Facebook开发者控制台](https://developers.facebook.com/)
2. 选择您的应用
3. 转到 "Facebook登录" > "设置"
4. 在"有效OAuth重定向URI"部分:
   - 删除旧的前端回调URI
   - 添加新的后端回调URI
5. 保存更改

## 🚀 部署注意事项

1. 确保后端配置了正确的OAuth密钥
2. 更新OAuth提供商的回调URL配置
3. 配置正确的CORS策略
4. 在生产环境中使用HTTPS
5. 定期轮换OAuth密钥
