import { getEnvironmentConfig } from '@/utils/environment'

// 获取环境配置
const envConfig = getEnvironmentConfig()

// 第三方登录配置 (仅前端公开信息)
export const authConfig = {
  google: {
    clientId: envConfig.google.clientId,
    authUrl: envConfig.google.authUrl,
    scope: envConfig.google.scope,
    // 回调地址指向前端 - 修复OAuth回调配置
    redirectUri: `${envConfig.frontendBaseUrl}/auth/google/callback`
  },
  facebook: {
    appId: envConfig.facebook.appId,
    authUrl: envConfig.facebook.authUrl,
    scope: envConfig.facebook.scope,
    // 回调地址指向前端 - 修复OAuth回调配置
    redirectUri: `${envConfig.frontendBaseUrl}/auth/facebook/callback`
  }
}

// 获取当前语言对应的OAuth语言代码
const getOAuthLanguage = () => {
  if (typeof window === 'undefined') return 'en'

  const currentLocale = localStorage.getItem('user-locale') || navigator.language || 'en-US'

  // 将i18n语言代码映射到OAuth提供商支持的语言代码
  const languageMap: Record<string, string> = {
    'zh-CN': 'zh-CN',
    'en-US': 'en',
    'ar-SA': 'ar',
    'es-ES': 'es',
    'fr-FR': 'fr'
  }

  return languageMap[currentLocale] || 'en'
}

// 生成OAuth授权URL的工具函数
export const generateOAuthUrl = (provider: 'google' | 'facebook', state?: string) => {
  const params = new URLSearchParams()
  const language = getOAuthLanguage()

  if (provider === 'google') {
    const googleConfig = authConfig.google
    params.append('client_id', googleConfig.clientId)
    params.append('redirect_uri', googleConfig.redirectUri)
    params.append('response_type', 'code')
    params.append('scope', googleConfig.scope)
    params.append('access_type', 'offline')
    params.append('prompt', 'consent')
    params.append('hl', language) // Google OAuth语言参数
    if (state) params.append('state', state)
    return `${googleConfig.authUrl}?${params.toString()}`
  } else if (provider === 'facebook') {
    const facebookConfig = authConfig.facebook
    params.append('client_id', facebookConfig.appId)
    params.append('redirect_uri', facebookConfig.redirectUri)
    params.append('response_type', 'code')
    params.append('scope', facebookConfig.scope)
    params.append('locale', language === 'zh-CN' ? 'zh_CN' : language) // Facebook OAuth语言参数
    if (state) params.append('state', state)
    return `${facebookConfig.authUrl}?${params.toString()}`
  }

  throw new Error(`Unsupported OAuth provider: ${provider}`)
}

// 设备检测
export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false

  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  ) || window.innerWidth <= 768
}

// 检测是否在应用内浏览器
export const isInAppBrowser = (): boolean => {
  if (typeof window === 'undefined') return false

  const ua = navigator.userAgent.toLowerCase()
  return /micromessenger|qq|alipay|weibo|douyin|tiktok/i.test(ua)
}

// 获取适合的OAuth策略
export const getOAuthStrategy = (): 'redirect' | 'popup' | 'unsupported' => {
  if (isMobile()) {
    if (isInAppBrowser()) {
      return 'unsupported' // 应用内浏览器建议使用原生登录
    }
    return 'redirect' // 移动端浏览器使用重定向
  }
  return 'popup' // 桌面端可以使用弹窗或重定向
}

// 验证配置
export const validateAuthConfig = () => {
  const errors: string[] = []
  const warnings: string[] = []

  // Google配置验证
  if (!authConfig.google.clientId || authConfig.google.clientId === '') {
    errors.push('Google Client ID 未配置')
  } else if (authConfig.google.clientId.includes('your-google-client-id')) {
    errors.push('Google Client ID 仍为占位符，请配置真实的Client ID')
  }

  // Facebook配置验证
  if (!authConfig.facebook.appId || authConfig.facebook.appId === '') {
    warnings.push('Facebook App ID 未配置，Facebook登录将不可用')
  } else if (authConfig.facebook.appId.includes('your-facebook-app-id')) {
    warnings.push('Facebook App ID 仍为占位符，请配置真实的App ID')
  }

  // 重定向URI验证
  if (authConfig.google.redirectUri.includes('localhost') && typeof window !== 'undefined' && window.location.protocol === 'https:') {
    warnings.push('生产环境建议使用HTTPS重定向URI')
  }

  return {
    isValid: errors.length === 0,
    hasWarnings: warnings.length > 0,
    errors,
    warnings,
    googleConfigured: authConfig.google.clientId && !authConfig.google.clientId.includes('your-google-client-id'),
    facebookConfigured: authConfig.facebook.appId && !authConfig.facebook.appId.includes('your-facebook-app-id')
  }
}

// Google OAuth 2.0 配置 (兼容旧代码)
export const googleAuthConfig = {
  client_id: authConfig.google.clientId,
  redirect_uri: authConfig.google.redirectUri,
  scope: authConfig.google.scope,
  response_type: 'code',
  access_type: 'offline',
  prompt: 'consent'
}

// Facebook OAuth 配置 (兼容旧代码)
export const facebookAuthConfig = {
  client_id: authConfig.facebook.appId,
  redirect_uri: authConfig.facebook.redirectUri,
  scope: authConfig.facebook.scope,
  response_type: 'code'
}
