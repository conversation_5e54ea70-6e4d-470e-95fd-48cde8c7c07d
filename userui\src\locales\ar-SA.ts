export default {
  // عام
  common: {
    submit: 'إرسال',
    skip: 'تخطي',
    next: 'المجموعة التالية',
    previous: 'المجموعة السابقة',
    loading: 'جاري التحميل...',
    logout: 'تسجيل الخروج',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    close: 'إغلاق',
    select: 'اختيار',
    unselect: 'إلغاء الاختيار',
    logoutSuccess: 'تم تسجيل الخروج بنجاح'
  },
  
  // صفحة تسجيل الدخول
  login: {
    title: 'نظام أبحاث المستخدمين',
    subtitle: 'يرجى اختيار طريقة تسجيل الدخول',
    googleLogin: 'تسجيل الدخول بـ Google',
    facebookLogin: 'تسجيل الدخول بـ Facebook',
    loginSuccess: 'تم تسجيل الدخول بنجاح',
    loginFailed: 'فشل تسجيل الدخول، يرجى المحاولة مرة أخرى',
    loggingIn: 'جاري تسجيل الدخول...',
    facebookNotConfigured: 'Facebook غير مُكوّن',
    facebookConfigNotice: 'تسجيل الدخول بـ Facebook غير مُكوّن',
    facebookConfigDesc: 'تسجيل الدخول بـ Facebook يتطلب تكوين App ID للعمل.',
    facebookConfigGuide: 'يرجى الرجوع إلى FACEBOOK_SETUP_GUIDE.md للتكوين.',
    inAppBrowserWarning: 'تم اكتشاف متصفح داخل التطبيق، يُنصح بنسخ الرابط إلى متصفح النظام للحصول على أفضل تجربة تسجيل دخول',
    configError: 'خطأ في التكوين',
    facebookNotConfiguredError: 'تسجيل الدخول بـ Facebook غير مُكوّن، يرجى الاتصال بالمدير لتكوين Facebook App ID',
    inAppBrowserDetected: 'تم اكتشاف متصفح داخل التطبيق',
    inAppBrowserDesc: 'لضمان نجاح تسجيل الدخول، يُنصح بالفتح في متصفح النظام',
    viewLoginGuide: 'عرض دليل تسجيل الدخول',
    copyPageLink: 'نسخ رابط الصفحة',
    pageLinkCopied: 'تم نسخ رابط الصفحة إلى الحافظة',
    recommendSystemBrowser: 'يُنصح بتسجيل الدخول باستخدام متصفح النظام للحصول على أفضل تجربة'
  },
  
  // صفحة اختيار المنتجات
  products: {
    title: 'اختيار المنتجات',
    groupInfo: 'المجموعة {current} / {total}',
    productCount: '({count} منتجات)',
    selectedCount: 'تم اختيار {selected} / {max} منتجات',
    selectionHint: 'يمكنك اختيار حتى {max} منتجات لكل مجموعة',
    noData: 'لا توجد بيانات منتجات متاحة',
    selectAtLeastOne: 'يرجى اختيار منتج واحد على الأقل في مجموعة',
    selectProductFirst: 'يرجى اختيار المنتجات قبل التخطي',
    logoutSuccess: 'تم تسجيل الخروج بنجاح',
    dislikeAll: 'لا أحب أي منها',
    dislikeAllTitle: 'تأكيد العملية',
    dislikeAllConfirm: 'هل أنت متأكد من أنك لا تحب جميع المنتجات؟',
    tooManySelected: 'يمكنك اختيار {max} منتجات كحد أقصى',
    noProductSelected: 'لم يتم اختيار أي منتجات حالياً'
  },
  
  // صفحة معلومات المستخدم
  userInfo: {
    title: 'إكمال الملف الشخصي',
    subtitle: 'يرجى ملء معلوماتك الأساسية لمساعدتنا في فهم احتياجاتك بشكل أفضل',
    name: 'الاسم',
    namePlaceholder: 'يرجى إدخال اسمك',
    nameRequired: 'يرجى إدخال الاسم',
    gender: 'الجنس',
    genderRequired: 'يرجى اختيار الجنس',
    male: 'ذكر',
    female: 'أنثى',
    birthday: 'تاريخ الميلاد',
    birthdayPlaceholder: 'يرجى اختيار تاريخ ميلادك',
    country: 'البلد',
    countryPlaceholder: 'يرجى إدخال بلدك',
    address: 'العنوان',
    addressPlaceholder: 'يرجى إدخال عنوانك التفصيلي',
    selectedProducts: 'المنتجات المختارة',
    totalSelected: 'إجمالي {count} منتجات مختارة',
    groupLabel: 'المجموعة {index}:',
    submitSuccess: 'تم الإرسال بنجاح! شكراً لمساعدتك، سنرسل لك هدية صغيرة كتقدير!',
    skipSuccess: 'تم حفظ بيانات التصويت، شكراً لمشاركتك!',
    submitFailed: 'فشل الإرسال، يرجى المحاولة مرة أخرى',
    skipFailed: 'فشل التخطي، يرجى المحاولة مرة أخرى'
  },
  
  // صفحة النهاية
  end: {
    title: 'تم الإرسال بنجاح!',
    message: 'شكراً لمشاركتك في أبحاث منتجاتنا!<br>ملاحظاتك القيمة ستساعدنا في تقديم منتجات وخدمات أفضل.',
    giftInfo: 'سنرسل لك هدية صغيرة كتقدير!',
    backToLogin: 'العودة إلى تسجيل الدخول'
  },
  
  // صفحة الإرسال المكتمل
  alreadySubmitted: {
    title: 'تم إكمال الاستطلاع',
    message: 'شكراً لمشاركتك! لقد قمت بإرسال بيانات الاستطلاع بنجاح. يمكن لكل مستخدم المشاركة مرة واحدة فقط.',
    info: 'لتغيير الحساب، يرجى تسجيل الخروج أولاً'
  },

  // صفحة الخدمة غير متاحة
  serviceUnavailable: {
    title: 'تم إيقاف الوصول للموقع',
    description: 'نعتذر، نظام الاستطلاع مغلق حالياً ولا يمكن تقديم الخدمة.',
    subDescription: 'سيقوم النظام بفحص حالة الخدمة تلقائياً، وسيتم التوجيه إلى صفحة تسجيل الدخول عند الاستعادة. انقر على زر التحديث للتحقق من حالة النظام.',
    refresh: 'تحديث الصفحة',
    contact: 'إذا كان لديك أي استفسارات، يرجى الاتصال بمدير النظام',
    systemRestored: 'تم استعادة نظام الاستطلاع، جاري التوجيه...',
    systemStillClosed: 'نظام الاستطلاع لا يزال مغلقاً، يرجى المحاولة لاحقاً',
    checkFailed: 'فشل فحص الحالة، يرجى المحاولة لاحقاً'
  },

  // رسائل الخطأ
  errors: {
    networkError: 'خطأ في الشبكة، يرجى التحقق من اتصالك',
    serverError: 'خطأ في الخادم، يرجى المحاولة لاحقاً',
    unknownError: 'خطأ غير معروف، يرجى المحاولة مرة أخرى'
  },

  // صفحة معاودة الاتصال للمصادقة
  auth: {
    processing: 'جاري معالجة معلومات تسجيل الدخول',
    processingDesc: 'يرجى الانتظار، نحن نتحقق من هويتك...',
    loginSuccess: 'تم تسجيل الدخول بنجاح! جاري التوجيه...',
    loginFailed: 'فشل تسجيل الدخول، يرجى المحاولة مرة أخرى',
    networkError: 'فشل الاتصال بالشبكة، يرجى التحقق من الشبكة والمحاولة مرة أخرى',
    serverError: 'الخادم غير متاح مؤقتاً، يرجى المحاولة لاحقاً',
    authError: 'فشل التفويض، يرجى تسجيل الدخول مرة أخرى',
    noAuthCode: 'لم يتم استلام رمز التفويض',
    securityError: 'فشل التحقق الأمني، يرجى تسجيل الدخول مرة أخرى',
    backendError: 'خطأ في استجابة الخادم الخلفي',
    userCancelled: 'تم إلغاء التفويض، العودة إلى صفحة تسجيل الدخول',
    noAuthCode: 'لم يتم الحصول على رمز التفويض',
    securityError: 'فشل التحقق الأمني، يرجى تسجيل الدخول مرة أخرى',
    loginSuccess: 'تم تسجيل الدخول بنجاح! جاري التوجيه...',
    loginFailed: 'فشل تسجيل الدخول',
    networkError: 'فشل الاتصال بالشبكة، يرجى التحقق من الشبكة والمحاولة مرة أخرى',
    serverError: 'الخادم غير متاح مؤقتًا، يرجى المحاولة مرة أخرى لاحقًا',
    authError: 'فشل التفويض، يرجى تسجيل الدخول مرة أخرى'
  },

  // صفحة دليل تسجيل الدخول للجوال
  mobileGuide: {
    title: '📱 دليل تسجيل الدخول للجوال',
    subtitle: 'للحصول على أفضل تجربة تسجيل دخول، يرجى اتباع هذه الخطوات',
    environmentDetection: '🔍 اكتشاف البيئة الحالية',
    deviceType: 'نوع الجهاز:',
    mobileDevice: 'جهاز محمول',
    desktopDevice: 'جهاز مكتبي',
    browserEnvironment: 'بيئة المتصفح:',
    recommendedMethod: 'الطريقة الموصى بها:',
    systemBrowser: 'متصفح النظام',
    wechatBrowser: 'متصفح WeChat المدمج',
    qqBrowser: 'متصفح QQ المدمج',
    alipayBrowser: 'متصفح Alipay المدمج',
    inAppBrowser: 'متصفح داخل التطبيق',
    directLogin: 'تسجيل دخول مباشر',
    copyToSystemBrowser: 'نسخ الرابط إلى متصفح النظام',
    mobileOptimizedLogin: 'تسجيل دخول محسن للجوال',
    inAppBrowserGuide: '⚠️ دليل تسجيل الدخول للمتصفح داخل التطبيق',
    inAppWarning: 'تم اكتشاف أنك تستخدم متصفحًا داخل التطبيق (مثل WeChat، QQ، إلخ)، مما قد يؤثر على وظيفة تسجيل الدخول للطرف الثالث.',
    step1Title: 'نسخ رابط الصفحة الحالية',
    step1Desc: 'انقر على الزر أدناه لنسخ رابط الصفحة',
    copyLink: '📋 نسخ الرابط',
    step2Title: 'فتح في متصفح النظام',
    step2Desc: 'افتح متصفح النظام في هاتفك (Safari، Chrome، إلخ)، الصق الرابط وقم بزيارته',
    step3Title: 'إكمال تسجيل الدخول',
    step3Desc: 'انقر على زر تسجيل الدخول بـ Google في متصفح النظام لإكمال المصادقة',
    mobileTips: '📱 نصائح تسجيل الدخول للجوال',
    tip1: 'تأكد من استقرار اتصال الشبكة',
    tip2: 'السماح بالنوافذ المنبثقة للمتصفح (إذا لزم الأمر)',
    tip3: 'لا تقم بتبديل التطبيقات أثناء تسجيل الدخول',
    openedInSystemBrowser: 'لقد فتحت في متصفح النظام',
    startLogin: 'بدء تسجيل الدخول',
    confirmed: '✅ تم التأكيد',
    confirmCopied: 'تأكيد نسخ الرابط',
    backToHome: 'العودة إلى الصفحة الرئيسية',
    linkCopied: 'تم نسخ الرابط إلى الحافظة',
    pleaseConfirmFirst: 'يرجى نسخ الرابط وفتحه في متصفح النظام أولاً'
  }
}
