# 生产环境配置 (Production Environment)

# ==================== 环境标识 ====================
NODE_ENV=production
VITE_ENV=production

# ==================== API 配置 ====================
# 生产环境 - 自动适配当前页面协议
# 无需手动配置，会自动检测页面协议并选择对应后端端口
VITE_API_HOST=localhost
VITE_API_TIMEOUT=15000

# ==================== 前端配置 ====================
# 前端服务地址
VITE_APP_BASE_URL=https://localhost:5173
VITE_APP_HOST=localhost
VITE_APP_PORT=5173

# ==================== 调试配置 ====================
# 生产模式配置
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=error
VITE_SHOW_CONSOLE_LOGS=false

# ==================== 其他配置 ====================
# 缓存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=600000
