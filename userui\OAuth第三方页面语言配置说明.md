# OAuth第三方页面语言配置说明

## 🎯 功能概述

实现了OAuth第三方登录页面（Google和Facebook）的语言自动适配功能，根据用户当前选择的语言自动显示对应语言的授权页面。

## ✨ 主要功能

### 1. 自动语言检测
- **用户语言获取**: 从localStorage获取用户设置的语言
- **浏览器语言回退**: 如果没有用户设置，使用浏览器语言
- **默认语言**: 最终回退到英语

### 2. 语言映射
- **中文 (zh-CN)**: 显示简体中文授权页面
- **英文 (en-US)**: 显示英文授权页面  
- **阿拉伯语 (ar-SA)**: 显示阿拉伯语授权页面
- **西班牙语 (es-ES)**: 显示西班牙语授权页面
- **法语 (fr-FR)**: 显示法语授权页面

### 3. OAuth提供商支持
- **Google OAuth**: 使用 `hl` 参数控制语言
- **Facebook OAuth**: 使用 `locale` 参数控制语言

## 🛠️ 技术实现

### 语言检测函数
```typescript
const getOAuthLanguage = () => {
  if (typeof window === 'undefined') return 'en'
  
  const currentLocale = localStorage.getItem('user-locale') || navigator.language || 'en-US'
  
  // 将i18n语言代码映射到OAuth提供商支持的语言代码
  const languageMap: Record<string, string> = {
    'zh-CN': 'zh-CN',
    'en-US': 'en',
    'ar-SA': 'ar',
    'es-ES': 'es',
    'fr-FR': 'fr'
  }
  
  return languageMap[currentLocale] || 'en'
}
```

### OAuth URL生成
```typescript
// Google OAuth - 添加hl参数
params.append('hl', language)

// Facebook OAuth - 添加locale参数  
params.append('locale', language === 'zh-CN' ? 'zh_CN' : language)
```

## 🌍 语言参数说明

### Google OAuth语言参数
- **参数名**: `hl` (host language)
- **支持格式**: 
  - `zh-CN` - 简体中文
  - `en` - 英语
  - `ar` - 阿拉伯语
  - `es` - 西班牙语
  - `fr` - 法语

### Facebook OAuth语言参数
- **参数名**: `locale`
- **支持格式**:
  - `zh_CN` - 简体中文（注意下划线格式）
  - `en` - 英语
  - `ar` - 阿拉伯语
  - `es` - 西班牙语
  - `fr` - 法语

## 📋 使用效果

### 用户体验流程
1. 用户在应用中选择语言（如中文）
2. 点击Google或Facebook登录
3. 自动跳转到对应语言的OAuth授权页面
4. 用户看到熟悉语言的授权界面

### 语言切换效果
- **中文用户**: 看到"您正在重新登录 UserResearch"
- **英文用户**: 看到"You're signing in to UserResearch"
- **阿拉伯语用户**: 看到阿拉伯语授权页面
- **其他语言**: 对应语言的授权页面

## 🔧 配置文件

### 修改的文件
- `src/config/auth.ts` - 添加语言检测和URL生成逻辑
- `src/services/authService.ts` - 使用更新后的URL生成函数

### 关键配置
```typescript
// 在generateOAuthUrl函数中自动添加语言参数
const language = getOAuthLanguage()

// Google
params.append('hl', language)

// Facebook  
params.append('locale', language === 'zh-CN' ? 'zh_CN' : language)
```

## 📝 注意事项

### 1. 第三方限制
- OAuth授权页面由Google/Facebook服务器提供
- 我们只能通过URL参数影响显示语言
- 不是所有语言都被第三方完全支持

### 2. 语言回退
- 如果第三方不支持某种语言，会自动回退到英语
- 确保用户始终能看到可理解的授权页面

### 3. 实时生效
- 语言参数在每次生成OAuth URL时动态获取
- 用户切换语言后立即生效，无需重启应用

## 🚀 测试建议

### 功能测试
1. 在不同语言设置下测试Google登录
2. 在不同语言设置下测试Facebook登录
3. 验证授权页面显示的语言是否正确
4. 测试语言切换后的即时生效

### 浏览器测试
1. 测试不同浏览器语言设置
2. 验证localStorage语言设置优先级
3. 测试无语言设置时的默认行为

## 📊 支持状态

| 语言 | Google OAuth | Facebook OAuth | 状态 |
|------|-------------|----------------|------|
| 中文 | ✅ 支持 | ✅ 支持 | 完全支持 |
| 英文 | ✅ 支持 | ✅ 支持 | 完全支持 |
| 阿拉伯语 | ✅ 支持 | ✅ 支持 | 完全支持 |
| 西班牙语 | ✅ 支持 | ✅ 支持 | 完全支持 |
| 法语 | ✅ 支持 | ✅ 支持 | 完全支持 |

现在用户在使用不同语言时，OAuth第三方授权页面会自动显示对应的语言，提供更好的用户体验。
