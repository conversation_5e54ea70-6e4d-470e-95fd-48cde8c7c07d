<template>
  <div class="login-container">
    <div class="page-container">
      <!-- 暂时移除语言切换器 -->
      <div class="language-switcher-container">
        <LanguageSwitcher />
      </div>

      <div class="login-content">
        <el-card class="login-card">
          <h2 class="login-title">{{ $t('login.title') }}</h2>
          <p class="login-subtitle">{{ $t('login.subtitle') }}</p>

          <div class="login-buttons">
            <!-- Google 登录按钮 -->
            <el-button
              class="google-login-btn"
              size="large"
              :loading="loading"
              @click="handleGoogleLogin"
            >
              <svg class="login-icon" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              {{ $t('login.googleLogin') }}
            </el-button>

            <!-- Facebook 登录按钮 -->
            <el-button
              class="facebook-login-btn"
              size="large"
              :loading="loading"
              :disabled="!configStatus.facebookReady"
              @click="handleFacebookLogin"
            >
              <svg class="login-icon facebook-icon" viewBox="0 0 24 24">
                <path fill="white" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span v-if="configStatus.facebookReady">{{ $t('login.facebookLogin') }}</span>
              <span v-else>{{ $t('login.facebookNotConfigured') }}</span>
            </el-button>

            <!-- 配置状态提示 -->
            <div v-if="!configStatus.facebookReady" class="config-notice">
              <el-alert
                :title="$t('login.facebookConfigNotice')"
                type="warning"
                :closable="false"
                show-icon
              >
                <template #default>
                  <p>{{ $t('login.facebookConfigDesc') }}</p>
                  <p>{{ $t('login.facebookConfigGuide') }}</p>
                </template>
              </el-alert>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

import { useUserStore } from '@/stores/user'
import { authService } from '@/services/authService'
import { validateAuthConfig, getOAuthStrategy, isMobile, isInAppBrowser } from '@/config/auth'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const router = useRouter()
const userStore = useUserStore()
const { t } = useI18n()


// 加载状态
const loading = ref(false)

// 配置状态
const configStatus = computed(() => {
  const validation = validateAuthConfig()
  return {
    googleReady: validation.googleConfigured,
    facebookReady: validation.facebookConfigured,
    hasErrors: !validation.isValid,
    hasWarnings: validation.hasWarnings,
    errors: validation.errors,
    warnings: validation.warnings
  }
})



// Google 登录处理 - 使用新的安全authService
const handleGoogleLogin = async () => {
  try {
    loading.value = true
    console.log('开始Google登录流程...')

    // 检测设备和环境
    const strategy = getOAuthStrategy()
    console.log('检测到的OAuth策略:', strategy)
    console.log('是否为移动设备:', isMobile())
    console.log('是否在应用内浏览器:', isInAppBrowser())

    // 应用内浏览器提示
    if (strategy === 'unsupported') {
      ElMessage.warning(t('login.inAppBrowserWarning'))
      // 仍然尝试登录，但给出提示
    }

    // 验证配置
    const configValidation = validateAuthConfig()
    if (!configValidation.isValid) {
      throw new Error(`${t('login.configError')}: ${configValidation.errors.join(', ')}`)
    }

    // 显示警告信息
    if (configValidation.hasWarnings) {
      console.warn('配置警告:', configValidation.warnings)
    }

    // 使用新的安全authService进行登录
    await authService.loginWithGoogle()

  } catch (error) {
    console.error('Google 登录失败:', error)
    ElMessage.error(error instanceof Error ? error.message : t('login.loginFailed'))
    loading.value = false
  }
}

// Facebook 登录处理
const handleFacebookLogin = async () => {
  try {
    loading.value = true
    console.log('开始Facebook登录流程...')

    // 检测设备和环境
    const strategy = getOAuthStrategy()
    console.log('检测到的OAuth策略:', strategy)

    // 应用内浏览器提示
    if (strategy === 'unsupported') {
      ElMessage.warning(t('login.inAppBrowserWarning'))
    }

    // 验证Facebook配置
    const configValidation = validateAuthConfig()
    if (!configValidation.facebookConfigured) {
      throw new Error(t('login.facebookNotConfiguredError'))
    }

    if (configValidation.hasWarnings) {
      console.warn('配置警告:', configValidation.warnings)
    }

    // 使用authService进行Facebook登录（直接跳转）
    await authService.loginWithFacebook()

  } catch (error) {
    console.error('Facebook 登录失败:', error)
    ElMessage.error(error instanceof Error ? error.message : t('login.loginFailed'))
    loading.value = false
  }
}

// 组件挂载时检查登录状态
onMounted(() => {
  // 不再重复初始化用户状态，路由守卫已经处理了
  // 如果已经登录，直接跳转到产品选择页面
  if (userStore.isLoggedIn) {
    console.log('用户已登录，跳转到产品页面')
    router.push('/products')
  }
})
</script>

<style scoped>
.login-container {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.login-container .page-container {
  width: 100%;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
}

.language-switcher-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

/* RTL支持 */
[dir="rtl"] .language-switcher-container {
  right: auto;
  left: 20px;
}

.login-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 100vh;
}

.login-card {
  width: 100%;
  max-width: 420px;
  padding: 40px 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  background: white;
  margin: 20px;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .login-card {
    margin: 15px;
    padding: 30px 24px;
    max-width: none;
  }
}

.login-title {
  text-align: center;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: bold;
  font-size: 28px;
}

.login-subtitle {
  text-align: center;
  margin-bottom: 32px;
  color: #7f8c8d;
  font-size: 16px;
}

/* 移动端登录页面适配 */
@media (max-width: 480px) {
  .login-title {
    font-size: 24px;
  }

  .login-subtitle {
    font-size: 14px;
    margin-bottom: 24px;
  }
}

.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.google-login-btn,
.facebook-login-btn {
  width: 100%;
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.google-login-btn {
  background: white;
  border: 2px solid #e0e0e0;
  color: #333;
}

.google-login-btn:hover {
  border-color: #4285f4;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
}

.facebook-login-btn {
  background: #1877f2;
  border: 2px solid #1877f2;
  color: white;
}

.facebook-login-btn:hover:not(:disabled) {
  background: #166fe5;
  border-color: #166fe5;
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.3);
}

.facebook-login-btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

.facebook-login-btn:disabled .login-icon path {
  fill: #999;
}

.config-notice {
  margin-top: 20px;
}

.config-notice .el-alert {
  border-radius: 8px;
}

.config-notice .el-alert__content p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.4;
}

.login-icon {
  width: 20px;
  height: 20px;
}

.facebook-icon {
  filter: brightness(0) invert(1);
}
</style>