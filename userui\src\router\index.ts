import { createRouter, createWebHistory } from 'vue-router'
import SurveyStatusService from '@/services/surveyStatusService'
import { isMobile, isInAppBrowser } from '@/config/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: { requiresAuth: false }
    },

    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/auth/:provider/callback',
      name: 'auth-callback',
      component: () => import('../views/AuthCallbackView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/products',
      name: 'products',
      component: () => import('../views/ProductListView.vue'),
      meta: { requiresAuth: true } // 产品列表页面需要登录
    },
    {
      path: '/user-info',
      name: 'user-info',
      component: () => import('../views/UserInfoView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/end',
      name: 'end',
      component: () => import('../views/EndView.vue'),
      meta: { requiresAuth: true, allowSubmitted: true }
    },
    {
      path: '/final',
      name: 'final',
      component: () => import('../views/FinalView.vue'),
      meta: { requiresAuth: true, allowSubmitted: true } // 允许已提交用户访问
    },
    {
      path: '/already-submitted',
      name: 'already-submitted',
      component: () => import('../views/AlreadySubmittedView.vue'),
      meta: { requiresAuth: true, allowSubmitted: true } // 允许已提交用户访问
    },
    {
      path: '/auth/success',
      name: 'auth-success',
      component: () => import('../views/AuthSuccessView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/service-unavailable',
      name: 'service-unavailable',
      component: () => import('../views/ServiceUnavailableView.vue'),
      meta: { requiresAuth: false }
    },
    // 移动端专用路由
    {
      path: '/mobile/login',
      name: 'mobile-login',
      component: () => import('../views/MobileLoginView.vue'),
      meta: { requiresAuth: false, mobile: true }
    },
    {
      path: '/mobile/auth/guide',
      name: 'mobile-auth-guide',
      component: () => import('../views/MobileAuthGuideView.vue'),
      meta: { requiresAuth: false, mobile: true }
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  console.log('路由守卫: 从', from.path, '到', to.path)

  // 第一优先级：检查调查状态（除了服务不可用页面）
  // 调查关闭时，所有页面都应该重定向到服务不可用页面
  if (to.name !== 'service-unavailable') {
    try {
      console.log('路由守卫: 检查调查状态，目标页面:', to.name)

      // 只在页面刷新或首次访问时检查状态
      if (from.name === undefined || from.name === 'service-unavailable') {
        console.log('路由守卫: 页面刷新或从服务不可用页面访问，强制检查状态')
        const isSurveyOpen = await SurveyStatusService.forceCheckSurveyStatus()
        console.log('路由守卫: 调查状态检查结果:', isSurveyOpen)

        if (!isSurveyOpen) {
          console.log('路由守卫: 调查已关闭，重定向到服务不可用页面')
          next('/service-unavailable')
          return
        }
      }
      console.log('路由守卫: 调查状态正常或跳过检查，继续后续检查')
    } catch (error) {
      console.error('路由守卫: 检查调查状态失败:', error)
      // 检查失败时继续正常流程，不阻塞用户访问
    }
  } else {
    console.log('路由守卫: 访问特殊页面，跳过调查状态检查')
  }

  // 动态导入userStore以避免循环依赖
  const { useUserStore } = await import('@/stores/user')
  const userStore = useUserStore()

  // 确保用户状态已初始化
  if (!userStore.isInitialized) {
    console.log('路由守卫: 初始化用户状态')
    await userStore.initUserState()
  }

  // 移动端登录页面重定向逻辑
  if (to.name === 'login') {
    const mobile = isMobile()
    const inApp = isInAppBrowser()

    // 如果是移动设备且在应用内浏览器，引导到移动端登录指南
    if (mobile && inApp && to.query.guide !== 'skip') {
      console.log('移动端应用内浏览器，重定向到登录指南')
      next('/mobile/auth/guide')
      return
    }

    // 如果是移动设备但不在应用内浏览器，可以使用移动端优化登录页
    if (mobile && !inApp && to.query.mobile !== 'skip') {
      console.log('移动端设备，重定向到移动端登录页')
      next('/mobile/login')
      return
    }
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    console.log('需要登录，重定向到登录页')
    // 根据设备类型选择登录页面
    const mobile = isMobile()
    const loginPath = mobile ? '/mobile/login' : '/login'
    next(loginPath)
    return
  }

  // 第三优先级：检查已提交用户的访问限制（仅在调查开放时）
  if (to.name !== 'service-unavailable' && userStore.isLoggedIn && userStore.hasSubmitted && !to.meta.allowSubmitted) {
    console.log('用户已提交，重定向到已提交页面')
    next('/already-submitted')
    return
  }

  // 第四优先级：已登录用户访问登录页的处理（首页由 HomeView 组件自己处理）
  if (to.name === 'login' && userStore.isLoggedIn) {
    if (userStore.hasSubmitted) {
      console.log('已提交用户访问登录页，重定向到已提交页面')
      next('/already-submitted')
    } else {
      console.log('已登录用户访问登录页，重定向到产品页面')
      next('/products')
    }
    return
  }

  next()
})

export default router
