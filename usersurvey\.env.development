# 开发环境配置 (Development Environment)

# ==================== 环境标识 ====================
NODE_ENV=development
VITE_ENV=development

# ==================== API 配置 ====================
# 后端API服务地址 - 自动适配协议
VITE_API_HOST=localhost
VITE_API_TIMEOUT=10000

# 协议切换开关 (true=HTTPS端口8081, false=HTTP端口8080)
VITE_USE_HTTPS=true

# ==================== 前端配置 ====================
# 前端服务地址
VITE_APP_BASE_URL=http://localhost:5173
VITE_APP_HOST=localhost
VITE_APP_PORT=5173

# ==================== 调试配置 ====================
# 开发模式配置
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug
VITE_SHOW_CONSOLE_LOGS=true

# ==================== 其他配置 ====================
# 缓存配置
VITE_CACHE_ENABLED=false
VITE_CACHE_DURATION=300000
