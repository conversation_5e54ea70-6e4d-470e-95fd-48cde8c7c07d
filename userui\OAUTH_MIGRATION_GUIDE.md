# OAuth配置迁移指南

## 🔄 从前端回调迁移到后端回调

为了提高安全性，我们已将OAuth流程从前端回调改为后端回调。本指南将帮助您完成迁移。

## 📋 迁移检查清单

### ✅ 已完成的前端更改
- [x] 移除前端的敏感OAuth配置 (Client Secret, App Secret)
- [x] 更新OAuth回调地址指向后端
- [x] 创建新的认证成功页面 (`/auth/success`)
- [x] 更新authService使用新的安全流程
- [x] 配置环境变量分离 (development/production)

### 🔧 需要手动完成的配置更改

#### 1. Google Cloud Console配置
**当前状态**: 重定向URI指向前端
**需要更改为**: 重定向URI指向后端

```
旧配置 (删除):
❌ http://localhost:3000/auth/google/callback
❌ http://localhost:5173/auth/google/callback

新配置 (添加):
✅ http://localhost:8080/api/auth/google/callback  (开发环境)
✅ https://localhost:8081/api/auth/google/callback (生产环境)
```

**操作步骤**:
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择项目 → APIs & Services → Credentials
3. 编辑OAuth 2.0客户端ID
4. 更新"授权重定向URI"
5. 保存更改

#### 2. Facebook开发者控制台配置
**当前状态**: 重定向URI指向前端
**需要更改为**: 重定向URI指向后端

```
旧配置 (删除):
❌ http://localhost:3000/auth/facebook/callback
❌ http://localhost:5173/auth/facebook/callback

新配置 (添加):
✅ http://localhost:8080/api/auth/facebook/callback  (开发环境)
✅ https://localhost:8081/api/auth/facebook/callback (生产环境)
```

**操作步骤**:
1. 访问 [Facebook开发者控制台](https://developers.facebook.com/)
2. 选择应用 → Facebook登录 → 设置
3. 更新"有效OAuth重定向URI"
4. 保存更改

#### 3. 后端API实现
**当前状态**: 后端已有OAuth配置，但可能需要更新API端点
**需要实现**: 新的回调处理端点

需要实现的API端点:
```csharp
// Google OAuth回调
[HttpGet("google/callback")]
public async Task<IActionResult> GoogleCallback(string code, string state)

// Facebook OAuth回调  
[HttpGet("facebook/callback")]
public async Task<IActionResult> FacebookCallback(string code, string state)

// 认证状态检查
[HttpGet("status")]
public async Task<IActionResult> GetAuthStatus()
```

## 🔄 新的OAuth流程

### 旧流程 (不安全)
```
用户登录 → OAuth授权 → 前端回调 → 前端调用后端API → 后端处理
```

### 新流程 (安全)
```
用户登录 → OAuth授权 → 后端回调 → 后端处理 → 重定向前端成功页面
```

## 🧪 测试迁移

### 1. 测试前准备
- [ ] 确认Google Cloud Console配置已更新
- [ ] 确认Facebook开发者控制台配置已更新
- [ ] 确认后端服务正在运行
- [ ] 确认前端使用正确的环境配置

### 2. 测试步骤
1. 启动后端服务 (端口8080/8081)
2. 启动前端服务 (端口3000)
3. 访问登录页面
4. 点击Google登录按钮
5. 完成Google授权
6. 验证是否重定向到 `/auth/success`
7. 验证用户信息是否正确显示
8. 重复测试Facebook登录

### 3. 故障排除

#### 问题: "redirect_uri_mismatch" 错误
**原因**: OAuth提供商的重定向URI配置不正确
**解决**: 检查并更新Google/Facebook控制台中的重定向URI配置

#### 问题: 后端回调404错误
**原因**: 后端API端点未实现或路由配置错误
**解决**: 确认后端实现了正确的回调端点

#### 问题: 前端无法获取用户信息
**原因**: 认证状态检查API未实现或token处理错误
**解决**: 检查后端的认证状态API实现

## 📊 配置对比

### 前端配置变化
```diff
# 旧配置
- VITE_GOOGLE_CLIENT_SECRET=secret
- VITE_FACEBOOK_APP_SECRET=secret
- VITE_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# 新配置
+ VITE_OAUTH_CALLBACK_BASE_URL=http://localhost:8080/api/auth
```

### 后端配置保持
```json
{
  "OAuth": {
    "Google": {
      "ClientId": "...",
      "ClientSecret": "..."
    },
    "Facebook": {
      "AppId": "...",
      "AppSecret": "..."
    }
  }
}
```

## 🚀 迁移完成验证

迁移完成后，确认以下功能正常:
- [ ] Google登录流程完整
- [ ] Facebook登录流程完整
- [ ] 用户信息正确获取
- [ ] 登录状态正确保持
- [ ] 页面跳转逻辑正确
- [ ] 错误处理正常工作

## 🔐 安全性提升

迁移后的安全改进:
- ✅ 敏感OAuth密钥不再暴露在前端
- ✅ 所有OAuth交换在后端安全环境中进行
- ✅ 减少了前端攻击面
- ✅ 符合OAuth 2.0安全最佳实践

完成迁移后，您的OAuth登录系统将更加安全可靠！
