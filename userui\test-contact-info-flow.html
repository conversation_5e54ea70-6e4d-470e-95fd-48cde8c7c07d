<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系信息流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>用户信息流程测试（修复后）</h1>
    
    <div class="test-section">
        <h2>1. 测试用户信息提交（简化字段）</h2>
        <p>测试修改后的用户信息提交API，只包含性别、年龄、国籍三个字段</p>

        <div class="form-group">
            <label>性别:</label>
            <select id="userSex">
                <option value="男">男</option>
                <option value="女">女</option>
            </select>
        </div>

        <div class="form-group">
            <label>年龄:</label>
            <select id="userAge">
                <option value="20以下">20以下</option>
                <option value="20-30岁">20-30岁</option>
                <option value="31-40岁">31-40岁</option>
                <option value="41-50岁">41-50岁</option>
                <option value="50岁以上">50岁以上</option>
            </select>
        </div>

        <div class="form-group">
            <label>国籍:</label>
            <input type="text" id="userCountry" value="中国" />
        </div>

        <button class="test-button" onclick="testUserInfoSubmit()">测试用户信息提交</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试联系信息提交</h2>
        <p>测试新的联系信息提交API</p>
        
        <div class="form-group">
            <label>用户ID:</label>
            <input type="number" id="contactUserId" value="1" />
        </div>
        
        <div class="form-group">
            <label>联系人姓名:</label>
            <input type="text" id="contactName" value="张三" />
        </div>
        
        <div class="form-group">
            <label>联系电话:</label>
            <input type="text" id="contactPhone" value="13800138000" />
        </div>
        
        <div class="form-group">
            <label>邮寄地址:</label>
            <textarea id="contactAddress">北京市朝阳区测试街道123号</textarea>
        </div>
        
        <button class="test-button" onclick="testContactInfoSubmit()">测试联系信息提交</button>
        <div id="result2" class="result"></div>
    </div>

    <script>
        async function testUserInfoSubmit() {
            try {
                const testData = {
                    userSex: document.getElementById('userSex').value,
                    country: document.getElementById('userCountry').value,
                    age: document.getElementById('userAge').value,
                    selectedProductIds: [1, 2], // 测试产品ID
                    thirdPartyId: 'test_user_123',
                    provider: 'test'
                };
                
                console.log('发送用户信息提交请求:', testData);
                
                const response = await fetch('/api/User/SubmitUserInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                console.log('用户信息提交响应:', result);
                
                document.getElementById('result1').innerHTML = 
                    '<h3>请求数据:</h3><pre>' + JSON.stringify(testData, null, 2) + '</pre>' +
                    '<h3>响应结果:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>' +
                    '<p class="' + (result.success ? 'success' : 'error') + '">' + 
                    (result.success ? '✓ 提交成功，用户ID: ' + result.userId : '✗ 提交失败: ' + result.message) + '</p>';
                    
                // 如果成功，保存用户ID用于联系信息测试
                if (result.success && result.userId) {
                    document.getElementById('contactUserId').value = result.userId;
                }
                    
            } catch (error) {
                console.error('用户信息提交失败:', error);
                document.getElementById('result1').innerHTML = 
                    '<p class="error">请求失败: ' + error.message + '</p>';
            }
        }

        async function testContactInfoSubmit() {
            try {
                const testData = {
                    userId: parseInt(document.getElementById('contactUserId').value),
                    name: document.getElementById('contactName').value,
                    phone: document.getElementById('contactPhone').value,
                    address: document.getElementById('contactAddress').value
                };
                
                console.log('发送联系信息提交请求:', testData);
                
                const response = await fetch('/api/User/SubmitContactInfo', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                console.log('联系信息提交响应:', result);
                
                document.getElementById('result2').innerHTML = 
                    '<h3>请求数据:</h3><pre>' + JSON.stringify(testData, null, 2) + '</pre>' +
                    '<h3>响应结果:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>' +
                    '<p class="' + (result.success ? 'success' : 'error') + '">' + 
                    (result.success ? '✓ 联系信息提交成功' : '✗ 提交失败: ' + result.message) + '</p>';
                    
            } catch (error) {
                console.error('联系信息提交失败:', error);
                document.getElementById('result2').innerHTML = 
                    '<p class="error">请求失败: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
