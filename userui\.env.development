# 开发环境配置 (Development Environment)

# ==================== 环境标识 ====================
NODE_ENV=development
VITE_ENV=development

# ==================== API 配置 ====================
# 后端API服务地址 - 自动适配协议
VITE_API_HOST=localhost
VITE_API_TIMEOUT=10000

# 协议切换开关 (true=HTTPS端口8081, false=HTTP端口8080)
VITE_USE_HTTPS=true

# API端点配置
VITE_API_AUTH_ENDPOINT=/api/auth
VITE_API_USER_ENDPOINT=/api/user
VITE_API_PRODUCT_ENDPOINT=/api/product
VITE_API_ADMIN_ENDPOINT=/api/admin
VITE_API_UPLOAD_ENDPOINT=/api/upload

# ==================== 前端配置 ====================
# 前端服务地址 - 自动适配协议
VITE_APP_BASE_URL=http://localhost:3000
VITE_APP_HOST=localhost
VITE_APP_PORT=3000

# ==================== OAuth 配置 ====================
# 前端只保留公开信息，敏感信息移到后端
# Google OAuth 公开配置
VITE_GOOGLE_CLIENT_ID=************-su2s5e82v75beq482m3dbb964rstp8ud.apps.googleusercontent.com
VITE_GOOGLE_AUTH_URL=https://accounts.google.com/o/oauth2/v2/auth
VITE_GOOGLE_SCOPE=openid email profile

# Facebook OAuth 公开配置
VITE_FACEBOOK_APP_ID=****************
VITE_FACEBOOK_AUTH_URL=https://www.facebook.com/v18.0/dialog/oauth
VITE_FACEBOOK_SCOPE=email,public_profile
# 注意：需要在Facebook开发者控制台中配置以下重定向URI：
# - http://localhost:3000/auth/facebook/callback (前端回调)
# - https://localhost:8081/api/auth/facebook/callback (后端HTTPS回调)

# OAuth 回调配置 - 自动适配后端协议
# 这个URL会在运行时根据VITE_USE_HTTPS自动选择
VITE_OAUTH_CALLBACK_BASE_URL=auto

# ==================== 安全配置 ====================
# HTTPS 配置
VITE_FORCE_HTTPS=false
VITE_SSL_ENABLED=false

# CORS 配置
VITE_CORS_ENABLED=true
VITE_CORS_ORIGIN=http://localhost:3000

# ==================== 调试配置 ====================
# 开发模式配置
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug
VITE_SHOW_CONSOLE_LOGS=true

# ==================== 其他配置 ====================
# 文件上传配置
VITE_UPLOAD_MAX_SIZE=5242880
VITE_UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# 缓存配置
VITE_CACHE_ENABLED=false
VITE_CACHE_DURATION=300000
