import { createRouter, createWebHistory } from 'vue-router'
import { useAdminStore } from '@/stores/admin'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/login' // 管理端重启后先跳转到登录页面
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresAuth: false, hideNavigation: true }
    },
    {
      path: '/products',
      name: 'products',
      component: () => import('../views/ProductListView.vue'),
      meta: { requiresAuth: true } // 产品列表页面需要登录
    },
    {
      path: '/user-info',
      name: 'user-info',
      component: () => import('../views/UserInfoView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/end',
      name: 'end',
      component: () => import('../views/EndView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true } // 管理员页面需要登录且需要管理员权限
    },
    {
      path: '/admin/add-product',
      name: 'add-product',
      component: () => import('../views/AddProductView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true } // 产品添加页面需要管理员权限
    },
    {
      path: '/admin/statistics',
      name: 'statistics',
      component: () => import('../views/StatisticsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true } // 统计页面需要管理员权限
    },
    {
      path: '/admin/users',
      name: 'user-management',
      component: () => import('../views/UserManagementView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true } // 用户管理页面需要管理员权限
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  console.log('路由守卫: 从', from.path, '到', to.path)

  const adminStore = useAdminStore()

  // 确保管理员状态已初始化
  adminStore.initAdminState()

  // 等待一个微任务，确保状态更新完成
  await new Promise(resolve => setTimeout(resolve, 0))

  console.log('当前登录状态:', adminStore.isLoggedIn)
  console.log('当前管理员信息:', adminStore.adminInfo)

  // 如果已登录管理员访问登录页，重定向到产品页面
  // 但如果是从首页重定向来的（项目重启），则允许访问登录页面
  if (to.path === '/login' && adminStore.isLoggedIn && from.path !== '/') {
    console.log('已登录管理员访问登录页，重定向到产品页面')
    next('/products')
    return
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth && !adminStore.isLoggedIn) {
    console.log('需要登录，重定向到登录页')
    if (to.path !== '/login') {
      ElMessage.warning('请先登录')
    }
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !adminStore.isAdmin) {
    console.log('需要管理员权限，重定向到产品页面')
    ElMessage.error('权限不足')
    next('/products')
    return
  }

  console.log('路由守卫通过，允许导航到:', to.path)
  next()
})

export default router
