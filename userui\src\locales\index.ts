import { createI18n } from 'vue-i18n'
import zhC<PERSON> from './zh-CN'
import enUS from './en-US'
import arSA from './ar-SA'
import esES from './es-ES'
import frFR from './fr-FR'

// 支持的语言列表
export const supportedLocales = [
  { code: 'zh-CN', name: '中文', nativeName: '中文' },
  { code: 'en-US', name: 'English', nativeName: 'English' },
  { code: 'ar-SA', name: 'Arabic', nativeName: 'العربية' },
  { code: 'es-ES', name: 'Spanish', nativeName: 'Español' },
  { code: 'fr-FR', name: 'French', nativeName: 'Français' }
]

// 语言映射表
const messages = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ar-SA': arSA,
  'es-ES': esES,
  'fr-FR': frFR
}

// 获取浏览器语言
function getBrowserLanguage(): string {
  const browserLang = navigator.language || navigator.languages[0]
  
  // 精确匹配
  if (messages[browserLang as keyof typeof messages]) {
    return browserLang
  }
  
  // 语言代码匹配（如 en-GB -> en-US）
  const langCode = browserLang.split('-')[0]
  const matchedLocale = supportedLocales.find(locale => 
    locale.code.startsWith(langCode)
  )
  
  if (matchedLocale) {
    return matchedLocale.code
  }
  
  // 默认返回英文
  return 'en-US'
}

// 获取默认语言
function getDefaultLocale(): string {
  // 优先从localStorage获取用户设置的语言
  const savedLocale = localStorage.getItem('user-locale')
  if (savedLocale && messages[savedLocale as keyof typeof messages]) {
    return savedLocale
  }
  
  // 其次使用浏览器语言
  return getBrowserLanguage()
}

// 保存语言设置
export function setLocale(locale: string) {
  localStorage.setItem('user-locale', locale)
  i18n.global.locale.value = locale as any
  
  // 设置HTML的lang属性
  document.documentElement.lang = locale
  
  // 设置HTML的dir属性（阿拉伯语需要从右到左）
  document.documentElement.dir = locale === 'ar-SA' ? 'rtl' : 'ltr'
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false,
  locale: getDefaultLocale(),
  fallbackLocale: 'en-US',
  messages,
  globalInjection: true
})

// 初始化时设置HTML属性
const currentLocale = getDefaultLocale()
document.documentElement.lang = currentLocale
document.documentElement.dir = currentLocale === 'ar-SA' ? 'rtl' : 'ltr'

export default i18n
