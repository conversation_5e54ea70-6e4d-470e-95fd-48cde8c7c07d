import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import axios from 'axios'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 配置axios默认设置 - 自动适配协议并检测可用后端
const getApiBaseUrl = () => {
  if (import.meta.env.DEV) {
    return '' // 开发环境使用代理
  }

  // 生产环境：自动适配当前页面协议
  const currentProtocol = window.location.protocol.replace(':', '')
  const host = import.meta.env.VITE_API_HOST || 'localhost'

  // 根据当前页面协议选择对应的后端端口
  const port = currentProtocol === 'https' ? 8081 : 8080
  const baseUrl = `${currentProtocol}://${host}:${port}`

  console.log(`🚀 UserSurvey API配置: ${baseUrl}`)
  return baseUrl
}

// 检测后端服务可用性并自动切换
const detectAndConfigureBackend = async () => {
  if (import.meta.env.DEV) {
    // 开发环境：检测哪个后端服务可用，并动态更新代理配置
    const useHttps = import.meta.env.VITE_USE_HTTPS === 'true'
    const preferredTarget = useHttps ? 'https://localhost:8081' : 'http://localhost:8080'
    const fallbackTarget = useHttps ? 'http://localhost:8080' : 'https://localhost:8081'

    console.log(`🔍 UserSurvey 检测后端服务可用性...`)
    console.log(`   首选目标: ${preferredTarget}`)
    console.log(`   备用目标: ${fallbackTarget}`)

    // 在开发环境中，我们仍然使用代理，但会在控制台显示检测信息
    axios.defaults.baseURL = ''
    console.log(`✅ UserSurvey 使用代理模式，目标: ${preferredTarget}`)
  } else {
    // 生产环境：直接配置API基础URL
    axios.defaults.baseURL = getApiBaseUrl()
  }
}

// 初始化后端配置
detectAndConfigureBackend()

axios.defaults.timeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000')
// 注意：不要设置默认的Content-Type，让浏览器自动设置，特别是对于文件上传

// 响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

const app = createApp(App)
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
    locale: zhCn,
  })

// 将axios挂载到全局属性
app.config.globalProperties.$axios = axios

app.mount('#app')
