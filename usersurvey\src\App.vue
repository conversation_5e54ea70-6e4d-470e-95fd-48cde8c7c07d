<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useAdminStore } from '@/stores/admin'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { onMounted } from 'vue'

const adminStore = useAdminStore()
const router = useRouter()

// 退出登录
const handleLogout = () => {
  adminStore.logout()
  ElMessage.success('Logged out successfully')
  router.push('/login')
}

// 初始化管理员状态 - 只在应用启动时初始化一次
onMounted(() => {
  // 路由守卫会处理状态初始化，这里不需要重复调用
  console.log('App component mounted')
})
</script>

<template>
  <div id="app">
    <!-- 导航栏 - 只在登录后显示 -->
    <el-header v-if="adminStore.isLoggedIn && !$route.meta.hideNavigation" class="app-header">
      <div class="header-content">
        <div class="nav-menu">
          <el-menu mode="horizontal" :ellipsis="false" router>
            <el-menu-item index="/products">产品选择</el-menu-item>
            <el-menu-item v-if="adminStore.isLoggedIn && adminStore.isAdmin" index="/admin">
              管理面板
            </el-menu-item>
          </el-menu>
        </div>
        <div class="user-actions">
          <template v-if="adminStore.isLoggedIn">
            <span class="welcome-text">欢迎，{{ adminStore.adminInfo?.userName }}</span>
            <el-button type="primary" @click="handleLogout" class="logout-btn">退出登录</el-button>
          </template>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main :class="['app-main', { 'no-header': !adminStore.isLoggedIn || $route.meta.hideNavigation }]">
      <RouterView />
    </el-main>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.app-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 0;
  height: 60px;
  line-height: 60px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
}

.nav-menu {
  flex: 1;
}

:deep(.el-menu--horizontal) {
  border-bottom: none;
  background: transparent;
}

:deep(.el-menu-item) {
  height: 60px;
  line-height: 60px;
  border-bottom: 2px solid transparent;
  border-radius: 8px 8px 0 0;
  margin: 0 4px;
  transition: all 0.3s ease;
  font-weight: 500;
}

:deep(.el-menu-item:hover) {
  background: linear-gradient(135deg, #ecf5ff 0%, #e3f2fd 100%);
  border-bottom-color: #409eff;
  transform: translateY(-1px);
}

:deep(.el-menu-item.is-active) {
  border-bottom-color: #409eff;
  color: #409eff;
  background: linear-gradient(135deg, #ecf5ff 0%, #e3f2fd 100%);
  font-weight: 600;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.welcome-text {
  color: #409eff;
  font-size: 14px;
  font-weight: 400;
  padding: 0;
  background: none;
  border-radius: 0;
  border: none;
}

.logout-btn {
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  border-radius: 4px !important;
  background: #409eff !important;
  border: 1px solid #409eff !important;
  color: white !important;
  transition: all 0.2s ease !important;
}

.logout-btn:hover {
  background: #66b1ff !important;
  border-color: #66b1ff !important;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3) !important;
}

.app-main {
  flex: 1;
  padding: 0;
  background: transparent;
  min-height: calc(100vh - 60px);
}

.app-main.no-header {
  padding-top: 0;
  min-height: 100vh;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .nav-menu {
    margin: 0 10px;
  }

  :deep(.el-menu-item) {
    margin: 0 2px;
    font-size: 14px;
  }

  .welcome-text {
    display: none;
  }

  .user-actions {
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 10px;
  }

  :deep(.el-menu-item) {
    margin: 0 1px;
    padding: 0 8px;
    font-size: 13px;
  }
}
</style>
