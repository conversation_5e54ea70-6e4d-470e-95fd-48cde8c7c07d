export default {
  // 通用
  common: {
    submit: '提交',
    skip: '跳过',
    next: '下一组',
    previous: '上一组',
    loading: '加载中...',
    logout: '退出登录',
    cancel: '取消',
    confirm: '确认',
    close: '关闭',
    select: '选择',
    unselect: '取消选择',
    logoutSuccess: '已退出登录'
  },
  
  // 登录页面
  login: {
    title: '用户调研系统',
    subtitle: '请选择登录方式',
    googleLogin: '使用 Google 登录',
    facebookLogin: '使用 Facebook 登录',
    loginSuccess: '登录成功',
    loginFailed: '登录失败，请重试',
    loggingIn: '登录中...',
    facebookNotConfigured: 'Facebook 未配置',
    facebookConfigNotice: 'Facebook登录未配置',
    facebookConfigDesc: 'Facebook登录功能需要配置App ID才能使用。',
    facebookConfigGuide: '请参考 FACEBOOK_SETUP_GUIDE.md 文件进行配置。',
    inAppBrowserWarning: '检测到您在应用内浏览器中，建议复制链接到系统浏览器中打开以获得最佳登录体验',
    configError: '配置错误',
    facebookNotConfiguredError: 'Facebook登录未配置，请联系管理员配置Facebook App ID',
    inAppBrowserDetected: '检测到应用内浏览器',
    inAppBrowserDesc: '为确保登录成功，建议在系统浏览器中打开',
    viewLoginGuide: '查看登录指南',
    copyPageLink: '复制页面链接',
    pageLinkCopied: '页面链接已复制到剪贴板',
    recommendSystemBrowser: '建议在系统浏览器中登录以获得最佳体验'
  },
  
  // 产品选择页面
  products: {
    title: '产品选择',
    groupInfo: '分组 {current} / {total}',
    productCount: '（{count} 个产品）',
    selectedCount: '已选择 {selected} / {max} 个产品',
    selectionHint: '每个分组最多可选择 {max} 个产品',
    noData: '暂无产品数据',
    selectAtLeastOne: '请至少在一个分组中选择产品',
    selectProductFirst: '请先选择产品再跳过',
    logoutSuccess: '已退出登录',
    dislikeAll: '都不喜欢',
    dislikeAllTitle: '确认操作',
    dislikeAllConfirm: '您确定您都不喜欢吗？',
    tooManySelected: '最多只能选择 {max} 个产品',
    noProductSelected: '目前没有选择产品'
  },
  
  // 用户信息页面
  userInfo: {
    title: '补充资料',
    subtitle: '请填写您的基本信息，这将帮助我们更好地了解您的需求',
    name: '姓名',
    namePlaceholder: '请输入您的姓名',
    nameRequired: '请输入姓名',
    gender: '性别',
    genderRequired: '请选择性别',
    male: '男',
    female: '女',
    age: '年龄',
    ageRequired: '请选择年龄',
    ageUnder20: '20以下',
    age20to30: '20-30岁',
    age31to40: '31-40岁',
    age41to50: '41-50岁',
    ageOver50: '50岁以上',
    country: '国籍',
    countryPlaceholder: '请输入您的国籍',
    countryRequired: '请输入国籍',
    address: '地址',
    addressPlaceholder: '请输入您的详细地址',
    selectedProducts: '您选择的产品',
    totalSelected: '共选择了 {count} 个产品',
    groupLabel: '分组 {index}:',
    submitSuccess: '提交成功！感谢您的帮助，我们会给您寄点小礼物以表感谢！',
    skipSuccess: '投票数据已保存，感谢您的参与！',
    submitFailed: '提交失败，请重试',
    skipFailed: '跳过失败，请重试'
  },

  // 结束页面
  end: {
    title: '提交成功！',
    message: '感谢您参与我们的产品调研！<br>您的宝贵意见将帮助我们提供更好的产品和服务。',
    giftInfo: '我们会给您寄点小礼物以表感谢！',
    contactTitle: '联系信息',
    contactSubtitle: '请填写您的联系信息，我们将为您寄送小礼物以表感谢！',
    contactName: '姓名',
    contactNamePlaceholder: '请输入您的姓名',
    contactNameRequired: '请输入姓名',
    contactPhone: '联系方式',
    contactPhonePlaceholder: '请输入您的联系方式',
    contactPhoneRequired: '请输入联系方式',
    contactAddress: '地址',
    contactAddressPlaceholder: '请输入详细的邮寄地址',
    contactAddressRequired: '请输入邮寄地址',
    submitSuccess: '提交成功！我们会尽快为您寄送小礼物！',
    skipSuccess: '感谢您的参与！',
    submitFailed: '提交失败，请重试',
    skipFailed: '跳过失败，请重试',
    finalMessage: '再次感谢您的参与！我们会尽快处理您的信息。',
    backToLogin: '返回登录页面'
  },
  
  // 最终完成页面
  final: {
    title: '您已完成调研',
    message: '感谢您的参与！您已经成功提交过调研数据，每位用户只能参与一次。',
    info: '如需更换账户，请先退出登录',
    backToLogin: '返回登录页面'
  },

  // 已提交页面
  alreadySubmitted: {
    title: '您已完成调研',
    message: '感谢您的参与！您已经成功提交过调研数据，每位用户只能参与一次。',
    info: '如需更换账户，请先退出登录'
  },

  // 服务不可用页面
  serviceUnavailable: {
    title: '网站已停止访问',
    description: '很抱歉，调研系统目前暂时关闭，无法提供服务。',
    subDescription: '系统会自动检测服务状态，一旦恢复将自动跳转到登录页面。点击刷新按钮可检查系统状态。',
    refresh: '刷新页面',
    contact: '如有疑问，请联系系统管理员',
    systemRestored: '调研系统已恢复，正在跳转...',
    systemStillClosed: '调研系统仍未开放，请稍后再试',
    checkFailed: '检查状态失败，请稍后再试'
  },

  // 错误信息
  errors: {
    networkError: '网络错误，请检查网络连接',
    serverError: '服务器错误，请稍后重试',
    unknownError: '未知错误，请重试'
  },

  // 认证回调页面
  auth: {
    processing: '正在处理登录信息',
    processingDesc: '请稍候，我们正在验证您的身份...',
    loginSuccess: '登录成功！正在跳转...',
    loginFailed: '登录失败，请重试',
    networkError: '网络连接失败，请检查网络后重试',
    serverError: '服务器暂时不可用，请稍后重试',
    authError: '授权失败，请重新登录',
    noAuthCode: '未获取到授权码',
    securityError: '安全验证失败，请重新登录',
    backendError: '后端响应错误',
    userCancelled: '已取消授权，返回登录页面',
    noAuthCode: '未获取到授权码',
    securityError: '安全验证失败，请重新登录',
    loginSuccess: '登录成功！正在跳转...',
    loginFailed: '登录失败',
    networkError: '网络连接失败，请检查网络后重试',
    serverError: '服务器暂时不可用，请稍后重试',
    authError: '授权失败，请重新登录'
  },

  // 移动端登录指南页面
  mobileGuide: {
    title: '📱 移动端登录指南',
    subtitle: '为了获得最佳登录体验，请按照以下步骤操作',
    environmentDetection: '🔍 当前环境检测',
    deviceType: '设备类型:',
    mobileDevice: '移动设备',
    desktopDevice: '桌面设备',
    browserEnvironment: '浏览器环境:',
    recommendedMethod: '推荐登录方式:',
    systemBrowser: '系统浏览器',
    wechatBrowser: '微信内置浏览器',
    qqBrowser: 'QQ内置浏览器',
    alipayBrowser: '支付宝内置浏览器',
    inAppBrowser: '应用内浏览器',
    directLogin: '直接登录',
    copyToSystemBrowser: '复制链接到系统浏览器',
    mobileOptimizedLogin: '移动端优化登录',
    inAppBrowserGuide: '⚠️ 应用内浏览器登录指导',
    inAppWarning: '检测到您正在使用应用内浏览器（如微信、QQ等），这可能影响第三方登录功能。',
    step1Title: '复制当前页面链接',
    step1Desc: '点击下方按钮复制页面链接',
    copyLink: '📋 复制链接',
    step2Title: '在系统浏览器中打开',
    step2Desc: '打开手机的系统浏览器（Safari、Chrome等），粘贴链接并访问',
    step3Title: '完成登录',
    step3Desc: '在系统浏览器中点击Google登录按钮完成认证',
    mobileTips: '📱 移动端登录提示',
    tip1: '确保网络连接稳定',
    tip2: '允许浏览器弹窗（如果需要）',
    tip3: '登录过程中请勿切换应用',
    openedInSystemBrowser: '我已在系统浏览器中打开',
    startLogin: '开始登录',
    confirmed: '✅ 已确认',
    confirmCopied: '确认已复制链接',
    backToHome: '返回首页',
    linkCopied: '链接已复制到剪贴板',
    pleaseConfirmFirst: '请先复制链接并在系统浏览器中打开'
  }
}
