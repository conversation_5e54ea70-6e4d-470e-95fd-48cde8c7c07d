import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface Product {
  id: number
  name: string
  imageUrl?: string
  groupIndex: number
  sortOrder: number
}

interface ProductGroup {
  groupIndex: number
  products: Product[]
}

export const useSurveyStore = defineStore('survey', () => {
  // 状态
  const productGroups = ref<ProductGroup[]>([])
  const currentGroupIndex = ref(0)
  const selectedProducts = ref<Product[]>([])
  const maxSelectionsPerGroup = 5

  // 计算属性
  const currentGroup = computed(() => {
    if (productGroups.value.length === 0 || currentGroupIndex.value >= productGroups.value.length) {
      return null
    }
    return productGroups.value[currentGroupIndex.value]
  })

  const currentProducts = computed(() => {
    return currentGroup.value ? currentGroup.value.products : []
  })

  const isLastGroup = computed(() => {
    return currentGroupIndex.value >= productGroups.value.length - 1
  })

  const canSelectMore = computed(() => {
    const currentGroupProducts = selectedProducts.value.filter(
      p => p.groupIndex === currentGroup.value?.groupIndex
    )
    return currentGroupProducts.length < maxSelectionsPerGroup
  })

  const currentGroupSelectedCount = computed(() => {
    if (!currentGroup.value) return 0
    return selectedProducts.value.filter(
      p => p.groupIndex === currentGroup.value.groupIndex
    ).length
  })

  // 方法
  const setProductGroups = (groups: ProductGroup[]) => {
    productGroups.value = groups
    currentGroupIndex.value = 0
  }

  const selectProduct = (product: Product) => {
    const currentGroupProducts = selectedProducts.value.filter(
      p => p.groupIndex === product.groupIndex
    )
    
    if (currentGroupProducts.length < maxSelectionsPerGroup) {
      const isAlreadySelected = selectedProducts.value.some(p => p.id === product.id)
      if (!isAlreadySelected) {
        selectedProducts.value.push(product)
      }
    }
  }

  const unselectProduct = (product: Product) => {
    const index = selectedProducts.value.findIndex(p => p.id === product.id)
    if (index > -1) {
      selectedProducts.value.splice(index, 1)
    }
  }

  const isProductSelected = (product: Product) => {
    return selectedProducts.value.some(p => p.id === product.id)
  }

  const nextGroup = () => {
    if (currentGroupIndex.value < productGroups.value.length - 1) {
      currentGroupIndex.value++
    }
  }

  const previousGroup = () => {
    if (currentGroupIndex.value > 0) {
      currentGroupIndex.value--
    }
  }

  const hasNextGroup = () => {
    return currentGroupIndex.value < productGroups.value.length - 1
  }

  const hasPreviousGroup = () => {
    return currentGroupIndex.value > 0
  }

  // 检查是否可以提交（至少有一个组选择了产品）
  const canSubmit = computed(() => {
    if (selectedProducts.value.length === 0) return false

    // 检查是否至少有一个组有选择的产品
    const groupsWithSelections = new Set()
    selectedProducts.value.forEach(product => {
      groupsWithSelections.add(product.groupIndex)
    })

    return groupsWithSelections.size > 0
  })

  // 检查当前组是否可以跳过（允许不选择任何产品）
  const canSkipCurrentGroup = () => {
    return true // 允许任何组都可以跳过
  }

  const reset = () => {
    productGroups.value = []
    currentGroupIndex.value = 0
    selectedProducts.value = []
  }

  const getSelectedProductsByGroup = () => {
    const result: { [key: number]: Product[] } = {}
    selectedProducts.value.forEach(product => {
      if (!result[product.groupIndex]) {
        result[product.groupIndex] = []
      }
      result[product.groupIndex].push(product)
    })
    return result
  }

  return {
    // 状态
    productGroups,
    currentGroupIndex,
    selectedProducts,
    maxSelectionsPerGroup,

    // 计算属性
    currentGroup,
    currentProducts,
    isLastGroup,
    canSelectMore,
    currentGroupSelectedCount,
    canSubmit,

    // 方法
    setProductGroups,
    selectProduct,
    unselectProduct,
    isProductSelected,
    nextGroup,
    previousGroup,
    hasNextGroup,
    hasPreviousGroup,
    canSkipCurrentGroup,
    reset,
    getSelectedProductsByGroup
  }
})
