/**
 * OAuth错误处理工具
 */
import { useI18n } from 'vue-i18n'

/**
 * 检查是否是用户取消授权的错误
 * @param error 错误信息
 * @returns 是否是用户取消授权
 */
export function isUserCancelledError(error: string): boolean {
  if (!error) return false
  
  const cancelErrors = [
    'access_denied',           // 标准的拒绝访问错误
    'user_cancelled',          // 用户取消
    'user_denied',             // 用户拒绝
    'cancelled_by_user',       // 用户取消
    'authorization_declined',  // 授权被拒绝
    'consent_required',        // 需要同意但用户拒绝
    'user_cancelled_login',    // 用户取消登录
    'authorization_cancelled', // 授权取消
    'login_cancelled'          // 登录取消
  ]
  
  return cancelErrors.some(cancelError => 
    error.toLowerCase().includes(cancelError.toLowerCase())
  )
}

/**
 * 获取用户友好的错误信息
 * @param error 原始错误信息
 * @returns 用户友好的错误信息
 */
export function getFriendlyErrorMessage(error: string): string {
  // 在工具函数中，我们需要使用静态的错误映射
  // 因为useI18n只能在Vue组件中使用
  if (isUserCancelledError(error)) {
    return 'Authorization cancelled, returning to login page'
  }

  // 其他常见错误的友好提示 - 使用英文作为默认
  const errorMap: Record<string, string> = {
    'invalid_request': 'Invalid request parameters, please try again',
    'invalid_client': 'Application configuration error, please contact administrator',
    'invalid_grant': 'Invalid authorization code, please login again',
    'unauthorized_client': 'Application not authorized, please contact administrator',
    'unsupported_grant_type': 'Unsupported grant type',
    'invalid_scope': 'Invalid permission scope',
    'server_error': 'Server error, please try again later',
    'temporarily_unavailable': 'Service temporarily unavailable, please try again later',
    'network_error': 'Network connection failed, please check network and try again',
    'timeout': 'Request timeout, please try again'
  }

  // 查找匹配的错误类型
  for (const [errorType, message] of Object.entries(errorMap)) {
    if (error.toLowerCase().includes(errorType.toLowerCase())) {
      return message
    }
  }

  // 默认错误信息
  return `Login failed: ${error}`
}

/**
 * OAuth错误处理结果
 */
export interface OAuthErrorHandleResult {
  /** 是否是用户取消 */
  isUserCancelled: boolean
  /** 友好的错误信息 */
  friendlyMessage: string
  /** 是否需要立即跳转（用户取消时立即跳转，其他错误延迟跳转） */
  shouldRedirectImmediately: boolean
}

/**
 * 处理OAuth错误
 * @param error 错误信息
 * @returns 处理结果
 */
export function handleOAuthError(error: string): OAuthErrorHandleResult {
  const isUserCancelled = isUserCancelledError(error)
  const friendlyMessage = getFriendlyErrorMessage(error)
  
  return {
    isUserCancelled,
    friendlyMessage,
    shouldRedirectImmediately: isUserCancelled
  }
}

/**
 * 记录OAuth错误日志
 * @param error 错误信息
 * @param provider OAuth提供商
 * @param context 上下文信息
 */
export function logOAuthError(error: string, provider: string, context?: any): void {
  const errorInfo = {
    error,
    provider,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    context
  }
  
  if (isUserCancelledError(error)) {
    console.log('OAuth user cancelled:', errorInfo)
  } else {
    console.error('OAuth error:', errorInfo)
  }
}
