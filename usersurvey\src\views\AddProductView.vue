<template>
  <div class="add-product-container">
    <el-card class="add-product-card">
      <template #header>
        <div class="card-header">
          <h2>添加产品</h2>
          <el-button @click="goBack" type="info" plain>
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        @submit.prevent="onSubmit"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model="productForm.name"
            placeholder="请输入产品名称"
            :disabled="loading"
            clearable
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="产品图片" prop="imageUrl">
          <div class="image-upload-container">
            <el-upload
              ref="uploadRef"
              class="image-uploader"
              action="/api/Imgs/Img"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :on-error="handleImageError"
              :before-upload="beforeImageUpload"
              :disabled="loading"
              accept="image/*"
            >
              <div v-if="productForm.imageUrl" class="image-preview">
                <img
                  :src="productForm.imageUrl"
                  alt="产品图片"
                  @load="handleImageLoad"
                  @error="handleImageDisplayError"
                />
                <div class="image-overlay">
                  <el-button type="danger" size="small" @click.stop="removeImage">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
                <!-- 调试信息 -->
                <div class="debug-info">
                  <small>URL: {{ productForm.imageUrl }}</small>
                </div>
              </div>
              <div v-else class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传图片</div>
                <div class="upload-hint">支持 JPG、PNG、GIF、WebP 格式，大小不超过 5MB</div>
              </div>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item label="产品分组" prop="groupIndex">
          <el-input-number
            v-model="productForm.groupIndex"
            :min="1"
            :max="100"
            :disabled="loading"
            placeholder="请输入分组索引"
            style="width: 200px"
          />
          <div class="form-hint">用于将产品分组显示，相同分组索引的产品会显示在一起</div>
        </el-form-item>

        <el-form-item label="排序顺序" prop="sortOrder">
          <el-input-number
            v-model="productForm.sortOrder"
            :min="1"
            :max="1000"
            :disabled="loading"
            placeholder="请输入排序顺序"
            style="width: 200px"
          />
          <div class="form-hint">数字越小排序越靠前</div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="onSubmit"
            :loading="loading"
            size="large"
          >
            {{ loading ? '添加中...' : '添加产品' }}
          </el-button>
          <el-button @click="resetForm" :disabled="loading" size="large">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadProps } from 'element-plus'
import { ArrowLeft, Plus, Delete } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()

// 表单引用
const productFormRef = ref<FormInstance>()
const uploadRef = ref()

// 加载状态
const loading = ref(false)

// 产品表单数据
const productForm = reactive({
  name: '',
  imageUrl: '',
  groupIndex: 1,
  sortOrder: 1
})

// 表单验证规则
const productRules: FormRules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 1, max: 100, message: '产品名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  imageUrl: [
    { required: true, message: '请上传产品图片', trigger: 'change' }
  ],
  groupIndex: [
    { required: true, message: '请输入产品分组', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '分组索引必须在 1 到 100 之间', trigger: 'blur' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序顺序', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '排序顺序必须在 1 到 1000 之间', trigger: 'blur' }
  ]
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 图片上传前的验证
 */
const beforeImageUpload: UploadProps['beforeUpload'] = (file) => {
  const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只能上传 JPG、PNG、GIF、WebP 格式的图片!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

/**
 * 动态获取API基础URL
 */
const getApiBaseUrl = () => {
  if (import.meta.env.DEV) {
    // 开发环境：根据 VITE_USE_HTTPS 选择
    const useHttps = import.meta.env.VITE_USE_HTTPS === 'true'
    return useHttps ? 'https://localhost:8081' : 'http://localhost:8080'
  }

  // 生产环境：自动适配当前页面协议
  const currentProtocol = window.location.protocol.replace(':', '')
  const host = import.meta.env.VITE_API_HOST || 'localhost'

  // 根据当前页面协议选择对应的后端端口
  const port = currentProtocol === 'https' ? 8081 : 8080
  return `${currentProtocol}://${host}:${port}`
}

/**
 * 修正图片URL以适配当前协议
 */
const fixImageUrl = (imageUrl: string) => {
  if (!imageUrl) return imageUrl

  // 如果是localhost但端口不对，修正端口
  if (imageUrl.includes('localhost:8080') || imageUrl.includes('localhost:8081')) {
    const apiBaseUrl = getApiBaseUrl()
    const urlParts = new URL(imageUrl)
    const correctedUrl = `${apiBaseUrl}${urlParts.pathname}`
    console.log('修正图片URL:', imageUrl, '->', correctedUrl)
    return correctedUrl
  }

  return imageUrl
}

/**
 * 图片上传成功回调
 */
const handleImageSuccess = (response: any) => {
  console.log('图片上传响应:', response)
  if (response && response.success) {
    // 优先使用原图URL，向后兼容
    const imageUrl = response.originalUrl || response.fileUrl || response.url

    if (!imageUrl) {
      console.error('后端响应中没有找到图片URL')
      ElMessage.error('图片上传成功但未返回URL')
      return
    }

    console.log('原始图片URL:', imageUrl)
    console.log('缩略图URL:', response.thumbnailUrl)
    console.log('是否生成缩略图:', response.hasThumbnail)

    // 修正图片URL以适配当前协议
    productForm.imageUrl = fixImageUrl(imageUrl)
    console.log('最终图片URL:', productForm.imageUrl)

    const successMessage = response.hasThumbnail ? '图片上传成功，已生成缩略图' : '图片上传成功'
    ElMessage.success(successMessage)
  } else {
    console.error('图片上传失败响应:', response)
    ElMessage.error(response?.message || '图片上传失败')
  }
}

/**
 * 图片上传失败回调
 */
const handleImageError = (error: any) => {
  console.error('上传错误:', error)
  ElMessage.error('图片上传失败，请检查网络连接后重试')
}

/**
 * 图片加载成功
 */
const handleImageLoad = () => {
  console.log('图片加载成功:', productForm.imageUrl)
}

/**
 * 图片显示错误
 */
const handleImageDisplayError = (event: Event) => {
  const img = event.target as HTMLImageElement
  console.error('图片显示失败:', {
    src: img.src,
    error: event
  })
  ElMessage.error('图片显示失败，请检查图片路径')
}

/**
 * 删除图片
 */
const removeImage = async () => {
  if (!productForm.imageUrl) return

  try {
    // 从URL中提取文件名
    const fileName = productForm.imageUrl.split('/').pop()

    if (fileName) {
      // 调用删除API
      const response = await axios.post('/api/Imgs/DeleteImg', {
        fileName: fileName
      })

      if (response.data.success) {
        productForm.imageUrl = ''
        ElMessage.success('图片已删除')
      } else {
        console.warn('删除图片失败:', response.data.message)
        // 即使删除失败，也清空URL，因为可能是文件已经不存在了
        productForm.imageUrl = ''
        ElMessage.success('图片已移除')
      }
    } else {
      productForm.imageUrl = ''
      ElMessage.success('图片已移除')
    }
  } catch (error) {
    console.error('删除图片时出错:', error)
    // 即使出错，也清空URL
    productForm.imageUrl = ''
    ElMessage.success('图片已移除')
  }
}

/**
 * 提交表单
 */
const onSubmit = async () => {
  if (!productFormRef.value) return

  try {
    // 表单验证
    await productFormRef.value.validate()

    loading.value = true

    // 打印调试信息
    console.log('准备提交产品数据:', {
      name: productForm.name,
      imageUrl: productForm.imageUrl,
      groupIndex: productForm.groupIndex,
      sortOrder: productForm.sortOrder
    })

    // 提交产品数据
    const response = await axios.post('/api/Product/AddProduct', {
      name: productForm.name,
      imageUrl: productForm.imageUrl,
      groupIndex: productForm.groupIndex,
      sortOrder: productForm.sortOrder
    })

    console.log('添加产品响应:', response.data)

    if (response.data.success) {
      ElMessage.success(response.data.message || '产品添加成功')
      // 重置表单
      resetForm()
    } else {
      console.error('产品添加失败:', response.data)

      // 检查是否是产品名称重复的错误
      if (response.data.message && response.data.message.includes('已存在')) {
        ElMessage({
          message: response.data.message,
          type: 'warning',
          duration: 8000,
          showClose: true
        })
        // 聚焦到产品名称输入框
        setTimeout(() => {
          const nameInput = document.querySelector('input[placeholder="请输入产品名称"]') as HTMLInputElement
          if (nameInput) {
            nameInput.focus()
            nameInput.select()
          }
        }, 100)
      } else {
        ElMessage.error(response.data.message || '添加产品失败')
      }
    }
  } catch (error: any) {
    console.error('添加产品请求失败:', error)

    // 详细的错误处理
    if (error.response) {
      // 服务器返回了错误响应
      console.error('服务器错误响应:', error.response.data)
      const errorMessage = error.response.data?.message || error.response.data || '服务器错误'
      ElMessage.error(`添加产品失败: ${errorMessage}`)
    } else if (error.request) {
      // 请求发送了但没有收到响应
      console.error('网络请求失败:', error.request)
      ElMessage.error('网络连接失败，请检查网络连接')
    } else {
      // 其他错误
      console.error('未知错误:', error.message)
      ElMessage.error(`添加产品失败: ${error.message}`)
    }
  } finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
const resetForm = () => {
  if (productFormRef.value) {
    productFormRef.value.resetFields()
  }
  productForm.name = ''
  productForm.imageUrl = ''
  productForm.groupIndex = 1
  productForm.sortOrder = 1
}
</script>

<style scoped>
.add-product-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.add-product-card {
  width: 100%;
  max-width: 800px;
  margin-top: 50px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.image-upload-container {
  width: 100%;
}

.image-uploader {
  width: 100%;
}

.image-preview {
  position: relative;
  width: 200px;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.3s;
}

.image-preview:hover {
  border-color: #409eff;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.debug-info {
  position: absolute;
  bottom: -25px;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  font-size: 10px;
  border-radius: 4px;
  word-break: break-all;
}

.upload-placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
  background: #fafafa;
}

.upload-placeholder:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 16px;
  margin-bottom: 8px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
  text-align: center;
  line-height: 1.4;
}

.form-hint {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}
</style>
