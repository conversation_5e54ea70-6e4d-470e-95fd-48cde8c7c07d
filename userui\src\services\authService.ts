import { generateOAuthUrl } from '@/config/auth'
import { getApiUrl, getRequestConfig } from '@/config/api'

export interface AuthUser {
  id: string
  name: string
  email: string
  avatar?: string
  provider: 'google' | 'facebook'
  hasSubmitted?: boolean
}

/**
 * 安全的OAuth认证服务
 * 所有敏感信息都在后端处理，前端只负责跳转和接收结果
 */
class AuthService {
  /**
   * Google 登录 - 安全跳转方式
   * 回调将直接到后端处理
   */
  async loginWithGoogle(): Promise<void> {
    try {
      console.log('Starting Google login flow')

      // 生成随机state用于CSRF保护
      const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

      // 保存state和provider信息用于后续验证
      sessionStorage.setItem('oauth_state', state)
      sessionStorage.setItem('oauth_provider', 'google')

      // 使用配置化的OAuth URL生成
      const authUrl = generateOAuthUrl('google', state)
      console.log('Google OAuth URL:', authUrl)

      // 跳转到Google授权页面（回调将直接到后端）
      window.location.href = authUrl
    } catch (error) {
      console.error('Google login failed:', error)
      throw error
    }
  }

  /**
   * Facebook 登录 - 安全跳转方式
   * 回调将直接到后端处理
   */
  async loginWithFacebook(): Promise<void> {
    try {
      console.log('Starting Facebook login flow')

      // 生成随机state用于CSRF保护
      const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

      // 保存state和provider信息用于后续验证
      sessionStorage.setItem('oauth_state', state)
      sessionStorage.setItem('oauth_provider', 'facebook')

      // 使用配置化的OAuth URL生成
      const authUrl = generateOAuthUrl('facebook', state)
      console.log('Facebook OAuth URL:', authUrl)

      // 跳转到Facebook授权页面（回调将直接到后端）
      window.location.href = authUrl
    } catch (error) {
      console.error('Facebook login failed:', error)
      throw error
    }
  }

  /**
   * 检查OAuth登录状态
   * 用于从后端获取登录结果
   */
  async checkAuthStatus(token?: string): Promise<AuthUser | null> {
    try {
      const apiUrl = getApiUrl('/api/auth/status')
      const requestConfig = getRequestConfig({
        method: 'GET',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        }
      })

      const response = await fetch(apiUrl, requestConfig)

      if (!response.ok) {
        return null
      }

      const result = await response.json()

      if (!result.success || !result.user) {
        return null
      }

      return {
        id: result.user.id,
        name: result.user.name,
        email: result.user.email,
        avatar: result.user.avatar,
        provider: result.user.provider,
        hasSubmitted: result.user.hasSubmitted
      }
    } catch (error) {
      console.error('Check auth status failed:', error)
      return null
    }
  }

  /**
   * 处理OAuth回调结果
   * 从URL参数中获取token或错误信息
   */
  handleOAuthCallback(): { success: boolean; token?: string; error?: string } {
    const urlParams = new URLSearchParams(window.location.search)
    const token = urlParams.get('token')
    const error = urlParams.get('error')
    const state = urlParams.get('state')

    // 验证state参数
    const savedState = sessionStorage.getItem('oauth_state')
    if (state && savedState && state !== savedState) {
      console.error('OAuth state verification failed')
      return { success: false, error: 'State verification failed' }
    }

    // 清理sessionStorage
    sessionStorage.removeItem('oauth_state')
    sessionStorage.removeItem('oauth_provider')

    if (error) {
      console.error('OAuth error:', error)
      return { success: false, error }
    }

    if (token) {
      // 保存token到localStorage
      localStorage.setItem('auth_token', token)
      return { success: true, token }
    }

    return { success: false, error: 'No valid authentication information received' }
  }

  /**
   * 登出
   */
  logout(): void {
    localStorage.removeItem('auth_token')
    sessionStorage.removeItem('oauth_state')
    sessionStorage.removeItem('oauth_provider')
  }

  /**
   * 获取保存的token
   */
  getToken(): string | null {
    return localStorage.getItem('auth_token')
  }
}

export const authService = new AuthService()
