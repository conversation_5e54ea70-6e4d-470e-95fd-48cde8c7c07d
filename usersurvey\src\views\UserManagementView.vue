<template>
  <div class="user-management-container">
    <el-card class="user-management-card">
      <template #header>
        <div class="card-header">
          <h2>用户管理</h2>
          <div class="header-actions">
            <el-button @click="refreshData" :loading="loading" type="primary">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button @click="goBack" type="info" plain>
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计概览 -->
      <div class="overview-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总用户数" :value="totalUsers" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="完整资料用户" :value="completedUsers" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="匿名用户" :value="anonymousUsers" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="今日新增" :value="todayNewUsers" />
          </el-col>
        </el-row>
      </div>

      <!-- 搜索和筛选 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户名、国家或地址"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterType" placeholder="筛选类型" @change="handleFilter">
              <el-option label="全部用户" value="all" />
              <el-option label="完整资料" value="completed" />
              <el-option label="匿名用户" value="anonymous" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="sortBy" placeholder="排序方式" @change="handleSort">
              <el-option label="注册时间（最新）" value="registerTime_desc" />
              <el-option label="注册时间（最早）" value="registerTime_asc" />
              <el-option label="用户名（A-Z）" value="userName_asc" />
              <el-option label="用户名（Z-A）" value="userName_desc" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 用户列表表格 -->
      <div class="table-section">
        <el-table 
          :data="filteredUsers" 
          :loading="loading"
          stripe
          style="width: 100%"
          @row-click="handleRowClick"
          row-class-name="clickable-row"
        >
          <el-table-column prop="id" label="用户ID" width="80" />
          <el-table-column prop="userName" label="用户名" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.userName === '匿名用户'" type="info" size="small">
                {{ scope.row.userName }}
              </el-tag>
              <span v-else>{{ scope.row.userName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="userSex" label="性别" width="80" />
          <el-table-column prop="country" label="国家" width="120" />
          <el-table-column prop="age" label="年龄" width="120" />
          <el-table-column prop="phone" label="联系电话" width="120" />
          <el-table-column prop="address" label="地址" width="200" show-overflow-tooltip />
          <el-table-column label="注册时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.registerTime) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isCompleted ? 'success' : 'warning'" size="small">
                {{ scope.row.isCompleted ? '完整' : '匿名' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button 
                type="primary" 
                size="small" 
                @click.stop="viewUserDetail(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalFilteredUsers"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详细信息"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedUserDetail" class="user-detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">{{ selectedUserDetail.id }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ selectedUserDetail.userName }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ selectedUserDetail.userSex || '未填写' }}</el-descriptions-item>
            <el-descriptions-item label="国家">{{ selectedUserDetail.country || '未填写' }}</el-descriptions-item>
            <el-descriptions-item label="年龄">
              {{ selectedUserDetail.age }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话" :span="2">
              {{ selectedUserDetail.phone || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="地址" :span="2">
              {{ selectedUserDetail.address || '未填写' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间" :span="2">
              {{ formatDateTime(selectedUserDetail.registerTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="资料状态" :span="2">
              <el-tag :type="selectedUserDetail.isCompleted ? 'success' : 'warning'">
                {{ selectedUserDetail.isCompleted ? '完整资料' : '匿名用户' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 投票记录 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <h3>投票记录 ({{ selectedUserDetail.voteRecords?.length || 0 }}条)</h3>
          </template>
          <div v-if="selectedUserDetail.voteRecords && selectedUserDetail.voteRecords.length > 0">
            <el-table :data="selectedUserDetail.voteRecords" stripe>
              <el-table-column label="产品图片" width="100">
                <template #default="scope">
                  <el-image
                    :src="scope.row.productImageUrl"
                    :preview-src-list="[scope.row.productImageUrl]"
                    style="width: 60px; height: 60px"
                    fit="cover"
                    preview-teleported
                  />
                </template>
              </el-table-column>
              <el-table-column prop="productName" label="产品名称" />
              <el-table-column prop="groupIndex" label="分组" width="80" />
              <el-table-column label="投票时间" width="180">
                <template #default="scope">
                  {{ formatDateTime(scope.row.voteTime) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="no-votes">
            <el-empty description="该用户暂无投票记录" />
          </div>
        </el-card>
      </div>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()

// 数据状态
const loading = ref(false)
const users = ref<any[]>([])
const selectedUserDetail = ref<any>(null)
const detailDialogVisible = ref(false)

// 搜索和筛选
const searchKeyword = ref('')
const filterType = ref('all')
const sortBy = ref('registerTime_desc')

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 用户数据接口
interface User {
  id: number
  userName: string
  userSex: string
  country: string
  age?: string
  phone:string
  address: string
  registerTime: string
  isCompleted: boolean
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 获取用户列表
 */
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/User/GetAllUsers')
    if (response.data.success) {
      users.value = response.data.data || []
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新数据
 */
const refreshData = () => {
  fetchUsers()
}

// 计算统计数据
const totalUsers = computed(() => users.value.length)

const completedUsers = computed(() => 
  users.value.filter(user => user.isCompleted).length
)

const anonymousUsers = computed(() => 
  users.value.filter(user => !user.isCompleted).length
)

const todayNewUsers = computed(() => {
  const today = new Date().toDateString()
  return users.value.filter(user => 
    new Date(user.registerTime).toDateString() === today
  ).length
})

/**
 * 筛选和搜索用户
 */
const filteredUsers = computed(() => {
  let filtered = [...users.value]

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.userName.toLowerCase().includes(keyword) ||
      (user.country && user.country.toLowerCase().includes(keyword)) ||
      (user.address && user.address.toLowerCase().includes(keyword))
    )
  }

  // 类型筛选
  if (filterType.value === 'completed') {
    filtered = filtered.filter(user => user.isCompleted)
  } else if (filterType.value === 'anonymous') {
    filtered = filtered.filter(user => !user.isCompleted)
  }

  // 排序
  switch (sortBy.value) {
    case 'registerTime_desc':
      filtered.sort((a, b) => new Date(b.registerTime).getTime() - new Date(a.registerTime).getTime())
      break
    case 'registerTime_asc':
      filtered.sort((a, b) => new Date(a.registerTime).getTime() - new Date(b.registerTime).getTime())
      break
    case 'userName_asc':
      filtered.sort((a, b) => a.userName.localeCompare(b.userName))
      break
    case 'userName_desc':
      filtered.sort((a, b) => b.userName.localeCompare(a.userName))
      break
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

const totalFilteredUsers = computed(() => {
  let filtered = [...users.value]

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(user => 
      user.userName.toLowerCase().includes(keyword) ||
      (user.country && user.country.toLowerCase().includes(keyword)) ||
      (user.address && user.address.toLowerCase().includes(keyword))
    )
  }

  if (filterType.value === 'completed') {
    filtered = filtered.filter(user => user.isCompleted)
  } else if (filterType.value === 'anonymous') {
    filtered = filtered.filter(user => !user.isCompleted)
  }

  return filtered.length
})

/**
 * 处理搜索
 */
const handleSearch = () => {
  currentPage.value = 1
}

/**
 * 处理筛选
 */
const handleFilter = () => {
  currentPage.value = 1
}

/**
 * 处理排序
 */
const handleSort = () => {
  currentPage.value = 1
}

/**
 * 处理分页大小变化
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

/**
 * 处理页码变化
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

/**
 * 处理行点击
 */
const handleRowClick = (row: User) => {
  viewUserDetail(row)
}

/**
 * 查看用户详情
 */
const viewUserDetail = async (user: User) => {
  loading.value = true
  try {
    const response = await axios.get(`/api/User/GetUserDetail?userId=${user.id}`)
    if (response.data.success) {
      selectedUserDetail.value = response.data.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取用户详情失败')
    }
  } catch (error: any) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭详情对话框
 */
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  selectedUserDetail.value = null
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}



// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.user-management-card {
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.overview-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.table-section {
  margin-bottom: 20px;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.user-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.no-votes {
  text-align: center;
  padding: 40px;
}

:deep(.clickable-row) {
  cursor: pointer;
}

:deep(.clickable-row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

:deep(.el-statistic__title) {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table th) {
  background: #f5f7fa;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-management-container {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .filter-section .el-row {
    flex-direction: column;
  }
  
  .filter-section .el-col {
    margin-bottom: 10px;
  }
}
</style>
