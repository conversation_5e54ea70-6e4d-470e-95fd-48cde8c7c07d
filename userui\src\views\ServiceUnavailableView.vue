<template>
  <div class="service-unavailable">
    <div class="container">
      <div class="content">
        <div class="icon-section">
          <el-icon size="120" color="#F56C6C">
            <CircleClose />
          </el-icon>
        </div>
        
        <div class="message-section">
          <h1>{{ $t('serviceUnavailable.title') }}</h1>
          <p class="description">{{ $t('serviceUnavailable.description') }}</p>
          <p class="sub-description">{{ $t('serviceUnavailable.subDescription') }}</p>
        </div>
        
        <div class="action-section">
          <el-button type="primary" @click="refreshPage" :loading="isChecking">
            <el-icon><Refresh /></el-icon>
            {{ isChecking ? $t('common.loading') : $t('serviceUnavailable.refresh') }}
          </el-button>
        </div>
        
        <div class="contact-section">
          <p class="contact-text">{{ $t('serviceUnavailable.contact') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { CircleClose, Refresh } from '@element-plus/icons-vue'
import SurveyStatusService from '@/services/surveyStatusService'
import { ElMessage } from 'element-plus'

const router = useRouter()
const { t } = useI18n()
const isChecking = ref(false)

// 刷新页面 - 先检查状态，如果系统开启则跳转到首页
const refreshPage = async () => {
  if (isChecking.value) return

  isChecking.value = true
  try {
    console.log('ServiceUnavailableView: 手动检查调研系统状态...')

    const isOpen = await SurveyStatusService.forceCheckSurveyStatus()
    console.log('ServiceUnavailableView: 状态检查结果:', isOpen)

    if (isOpen) {
      console.log('ServiceUnavailableView: 调研系统已开启，跳转到首页')
      ElMessage.success(t('serviceUnavailable.systemRestored'))

      setTimeout(() => {
        router.replace('/')
      }, 1000)
    } else {
      console.log('ServiceUnavailableView: 调研系统仍未开启')
      ElMessage.info(t('serviceUnavailable.systemStillClosed'))
    }
  } catch (error) {
    console.error('ServiceUnavailableView: 检查状态失败:', error)
    ElMessage.error(t('serviceUnavailable.checkFailed'))
  } finally {
    isChecking.value = false
  }
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('ServiceUnavailableView: 页面已加载，用户可手动刷新检查状态')
})

// 组件卸载时的清理
onUnmounted(() => {
  console.log('ServiceUnavailableView: 页面卸载')
})
</script>

<style scoped>
.service-unavailable {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  max-width: 600px;
  width: 100%;
}

.content {
  background: white;
  border-radius: 16px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-section {
  margin-bottom: 30px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.message-section h1 {
  font-size: 32px;
  color: #2c3e50;
  margin-bottom: 20px;
  font-weight: 600;
  line-height: 1.2;
}

.description {
  font-size: 18px;
  color: #5a6c7d;
  margin-bottom: 15px;
  line-height: 1.6;
}

.sub-description {
  font-size: 16px;
  color: #8492a6;
  margin-bottom: 40px;
  line-height: 1.5;
}

.action-section {
  margin-bottom: 40px;
}

:deep(.el-button) {
  padding: 15px 30px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.contact-section {
  border-top: 1px solid #e8ecf0;
  padding-top: 30px;
}

.contact-text {
  font-size: 14px;
  color: #8492a6;
  margin: 0;
  line-height: 1.5;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .service-unavailable {
    padding: 15px;
  }
  
  .content {
    padding: 40px 30px;
  }
  
  .message-section h1 {
    font-size: 28px;
  }
  
  .description {
    font-size: 16px;
  }
  
  .sub-description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 30px 20px;
  }
  
  .message-section h1 {
    font-size: 24px;
  }
  
  .description {
    font-size: 15px;
  }
  
  .sub-description {
    font-size: 13px;
  }
  
  :deep(.el-button) {
    padding: 12px 24px;
    font-size: 14px;
  }
}

/* RTL支持 */
[dir="rtl"] .content {
  text-align: center;
}
</style>
