import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

// 管理员信息接口
export interface AdminInfo {
  id: number
  userName: string
  role: string
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  message: string
  data?: AdminInfo
}

// 登录请求接口
export interface LoginRequest {
  userName: string
  password: string
}

/**
 * 管理员状态管理Store
 * 用于处理管理员登录、登出、状态管理等功能
 */
export const useAdminStore = defineStore('admin', () => {
  // 状态
  const isLoggedIn = ref(false)
  const adminInfo = ref<AdminInfo | null>(null)
  const loading = ref(false)
  const initialized = ref(false)

  /**
   * 管理员登录
   * @param loginData 登录数据（用户名和密码）
   * @returns 登录结果
   */
  const login = async (loginData: LoginRequest): Promise<LoginResponse> => {
    loading.value = true
    try {
      const response = await axios.post('/api/Admin/Login', loginData)
      const result: LoginResponse = response.data

      if (result.success && result.data) {
        // 登录成功，保存管理员信息
        isLoggedIn.value = true
        adminInfo.value = result.data
        
        // 保存到localStorage
        localStorage.setItem('adminInfo', JSON.stringify(result.data))
        localStorage.setItem('isAdminLoggedIn', 'true')
      }

      return result
    } catch (error) {
      console.error('管理员登录请求失败:', error)
      return {
        success: false,
        message: '网络错误，请稍后重试'
      }
    } finally {
      loading.value = false
    }
  }

  /**
   * 管理员登出
   * 清除登录状态和本地存储的信息
   */
  const logout = () => {
    isLoggedIn.value = false
    adminInfo.value = null
    localStorage.removeItem('adminInfo')
    localStorage.removeItem('isAdminLoggedIn')
  }

  /**
   * 初始化管理员状态
   * 从localStorage恢复登录状态
   */
  const initAdminState = () => {
    // 避免重复初始化
    if (initialized.value) {
      return
    }

    console.log('初始化管理员状态...')
    const savedAdminInfo = localStorage.getItem('adminInfo')
    const savedLoginState = localStorage.getItem('isAdminLoggedIn')

    if (savedLoginState === 'true' && savedAdminInfo) {
      try {
        adminInfo.value = JSON.parse(savedAdminInfo)
        isLoggedIn.value = true
        console.log('恢复管理员登录状态成功:', adminInfo.value)
      } catch (error) {
        console.error('恢复管理员状态失败:', error)
        logout()
      }
    } else {
      console.log('没有保存的登录状态')
    }

    initialized.value = true
  }

  /**
   * 检查是否为管理员
   * @returns 是否具有管理员权限
   */
  const isAdmin = computed(() => {
    const role = adminInfo.value?.role
    if (!role) return false

    // 支持多种角色格式
    const validRoles = ['admin', 'Admin', '管理员', 'administrator', 'Administrator']
    return validRoles.includes(role)
  })

  return {
    // 状态
    isLoggedIn,
    adminInfo,
    loading,
    
    // 计算属性
    isAdmin,
    
    // 方法
    login,
    logout,
    initAdminState
  }
})
