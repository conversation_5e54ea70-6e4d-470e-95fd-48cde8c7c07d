import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userService } from '@/services/userService'

// 用户信息接口
export interface UserInfo {
  id: string
  userName: string
  email: string
  avatar?: string
  provider: 'facebook' | 'google' // 第三方登录提供商
  providerId: string // 第三方用户ID
}

/**
 * 用户状态管理Store
 * 用于处理用户登录状态和第三方认证
 */
export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)
  const loading = ref(false)
  const hasSubmitted = ref(false) // 用户是否已提交过调研
  const userId = ref<number | null>(null) // 当前用户的数据库ID
  const isInitialized = ref(false) // 是否已初始化
  const isCheckingSubmission = ref(false) // 是否正在检查提交状态

  /**
   * 第三方登录
   * @param provider 登录提供商
   * @param userData 用户数据
   */
  const loginWithProvider = async (provider: 'facebook' | 'google', userData: any) => {
    try {
      loading.value = true

      const userInfo: UserInfo = {
        id: userData.id,
        userName: userData.name,
        email: userData.email,
        avatar: userData.picture || userData.avatar,
        provider: provider,
        providerId: userData.id
      }

      currentUser.value = userInfo
      isLoggedIn.value = true

      // 如果OAuth返回了hasSubmitted状态，直接使用
      if (userData.hasSubmitted !== undefined) {
        hasSubmitted.value = userData.hasSubmitted
        localStorage.setItem('hasSubmitted', hasSubmitted.value.toString())
        console.log('使用OAuth返回的提交状态:', hasSubmitted.value)
      } else {
        // 否则检查用户是否已提交过调研
        console.log('OAuth未返回提交状态，需要检查用户提交状态')
        await checkUserSubmissionStatus()
      }

      // 保存到本地存储
      localStorage.setItem('currentUser', JSON.stringify(userInfo))
      localStorage.setItem('isLoggedIn', 'true')

      return { success: true, message: '登录成功' }
    } catch (error) {
      console.error('第三方登录失败:', error)
      return { success: false, message: '登录失败' }
    } finally {
      loading.value = false
    }
  }

  /**
   * 检查用户是否已提交过调研
   */
  const checkUserSubmissionStatus = async () => {
    if (!currentUser.value || isCheckingSubmission.value) return

    isCheckingSubmission.value = true
    try {
      console.log('开始检查用户提交状态...')
      const result = await userService.checkSubmissionStatus(
        currentUser.value.providerId,
        currentUser.value.provider
      )

      if (result.success) {
        hasSubmitted.value = result.hasSubmitted
        // 保存到本地存储
        localStorage.setItem('hasSubmitted', hasSubmitted.value.toString())
        console.log('用户提交状态检查完成:', hasSubmitted.value)
      }
    } catch (error) {
      console.error('检查提交状态失败:', error)
      // 如果检查失败，默认为未提交
      hasSubmitted.value = false
    } finally {
      isCheckingSubmission.value = false
    }
  }

  /**
   * 标记用户已提交
   */
  const markAsSubmitted = () => {
    hasSubmitted.value = true
    localStorage.setItem('hasSubmitted', 'true')
  }

  /**
   * 设置用户ID
   */
  const setUserId = (id: number) => {
    userId.value = id
    localStorage.setItem('userId', id.toString())
  }

  /**
   * 退出登录
   */
  const logout = () => {
    currentUser.value = null
    isLoggedIn.value = false
    hasSubmitted.value = false
    userId.value = null
    isInitialized.value = false
    isCheckingSubmission.value = false
    localStorage.removeItem('currentUser')
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('hasSubmitted')
    localStorage.removeItem('userId')
  }

  /**
   * 初始化用户状态
   * 从localStorage恢复用户信息
   */
  const initUserState = async () => {
    if (isInitialized.value) {
      console.log('用户状态已初始化，跳过重复初始化')
      return
    }

    console.log('开始初始化用户状态...')
    const savedUser = localStorage.getItem('currentUser')
    const savedLoginState = localStorage.getItem('isLoggedIn')
    const savedSubmissionState = localStorage.getItem('hasSubmitted')
    const savedUserId = localStorage.getItem('userId')

    if (savedLoginState === 'true' && savedUser) {
      try {
        currentUser.value = JSON.parse(savedUser)
        isLoggedIn.value = true
        hasSubmitted.value = savedSubmissionState === 'true'

        // 恢复userId
        if (savedUserId) {
          userId.value = parseInt(savedUserId)
          console.log('恢复userId:', userId.value)
        }

        console.log('用户状态恢复完成')
      } catch (error) {
        console.error('恢复用户状态失败:', error)
        logout()
      }
    }

    isInitialized.value = true
    console.log('用户状态初始化完成')
  }

  /**
   * 手动检查用户提交状态（仅在需要时调用）
   */
  const manualCheckSubmissionStatus = async () => {
    if (!currentUser.value) {
      console.log('没有当前用户，跳过提交状态检查')
      return
    }

    console.log('手动检查用户提交状态...')
    await checkUserSubmissionStatus()
  }

  return {
    // 状态
    currentUser,
    isLoggedIn,
    loading,
    hasSubmitted,
    userId,
    isInitialized,
    isCheckingSubmission,

    // 方法
    loginWithProvider,
    logout,
    initUserState,
    checkUserSubmissionStatus,
    manualCheckSubmissionStatus,
    markAsSubmitted,
    setUserId
  }
})
