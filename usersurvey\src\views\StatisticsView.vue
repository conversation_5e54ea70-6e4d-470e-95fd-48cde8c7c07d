<template>
  <div class="statistics-container">
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <h2>产品投票统计</h2>
          <div class="header-actions">
            <el-button @click="refreshData" :loading="loading" type="primary">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button @click="goBack" type="info" plain>
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计概览 -->
      <div class="overview-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总投票数" :value="totalVotes" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="参与用户数" :value="totalUsers" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="产品总数" :value="totalProducts" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="最受欢迎产品" :value="mostPopularProduct" />
          </el-col>
        </el-row>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 柱状图 -->
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <h3>产品投票排行榜</h3>
              </template>
              <div class="chart-container">
                <v-chart 
                  class="chart" 
                  :option="barChartOption" 
                  :loading="loading"
                  autoresize
                />
              </div>
            </el-card>
          </el-col>
          
          <!-- 饼图 -->
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <h3>投票占比分布</h3>
              </template>
              <div class="chart-container">
                <v-chart 
                  class="chart" 
                  :option="pieChartOption" 
                  :loading="loading"
                  autoresize
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细数据表格 -->
      <div class="table-section">
        <el-card shadow="hover">
          <template #header>
            <h3>详细统计数据</h3>
          </template>
          <el-table 
            :data="statisticsData" 
            :loading="loading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="productName" label="产品名称" width="200" />
            <el-table-column label="产品图片" width="120">
              <template #default="scope">
                <el-image
                  :src="scope.row.imageUrl"
                  :preview-src-list="[scope.row.imageUrl]"
                  style="width: 60px; height: 60px"
                  fit="cover"
                  preview-teleported
                />
              </template>
            </el-table-column>
            <el-table-column prop="groupIndex" label="分组" width="80" />
            <el-table-column prop="voteCount" label="投票数" width="100" sortable />
            <el-table-column label="投票占比" width="120">
              <template #default="scope">
                <el-progress 
                  :percentage="getVotePercentage(scope.row.voteCount)" 
                  :color="getProgressColor(scope.row.voteCount)"
                />
              </template>
            </el-table-column>
            <el-table-column label="排名" width="80">
              <template #default="scope">
                <el-tag 
                  :type="getRankTagType(scope.$index + 1)"
                  size="small"
                >
                  第{{ scope.$index + 1 }}名
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  BarChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()

// 数据状态
const loading = ref(false)
const statisticsData = ref<any[]>([])

// 统计数据接口
interface VoteStatistics {
  productId: number
  productName: string
  imageUrl: string
  groupIndex: number
  sortOrder: number
  voteCount: number
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 获取投票统计数据
 */
const fetchStatistics = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/User/GetVoteStatistics')
    if (response.data.success) {
      statisticsData.value = response.data.data || []
    } else {
      ElMessage.error(response.data.message || '获取统计数据失败')
    }
  } catch (error: any) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新数据
 */
const refreshData = () => {
  fetchStatistics()
}

// 计算统计概览数据
const totalVotes = computed(() => {
  return statisticsData.value.reduce((sum, item) => sum + item.voteCount, 0)
})

const totalUsers = computed(() => {
  // 这里可以根据实际需求调整计算逻辑
  return Math.ceil(totalVotes.value * 0.8) // 假设平均每人投票1.25次
})

const totalProducts = computed(() => {
  return statisticsData.value.length
})

const mostPopularProduct = computed(() => {
  if (statisticsData.value.length === 0) return '-'
  return statisticsData.value[0]?.productName || '-'
})

/**
 * 获取投票百分比
 */
const getVotePercentage = (voteCount: number) => {
  if (totalVotes.value === 0) return 0
  return Math.round((voteCount / totalVotes.value) * 100)
}

/**
 * 获取进度条颜色
 */
const getProgressColor = (voteCount: number) => {
  const percentage = getVotePercentage(voteCount)
  if (percentage >= 20) return '#67c23a'
  if (percentage >= 10) return '#e6a23c'
  return '#f56c6c'
}

/**
 * 获取排名标签类型
 */
const getRankTagType = (rank: number) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

// 柱状图配置
const barChartOption = computed(() => ({
  title: {
    text: '产品投票数排行',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: statisticsData.value.slice(0, 10).map(item => item.productName),
    axisLabel: {
      rotate: 45,
      interval: 0
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '投票数',
    type: 'bar',
    data: statisticsData.value.slice(0, 10).map(item => item.voteCount),
    itemStyle: {
      color: '#409eff'
    }
  }]
}))

// 饼图配置
const pieChartOption = computed(() => ({
  title: {
    text: '投票占比分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [{
    name: '投票数',
    type: 'pie',
    radius: '50%',
    data: statisticsData.value.slice(0, 8).map(item => ({
      value: item.voteCount,
      name: item.productName
    })),
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))

// 组件挂载时获取数据
onMounted(() => {
  fetchStatistics()
})
</script>

<style scoped>
.statistics-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.statistics-card {
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.overview-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-container {
  height: 400px;
}

.chart {
  height: 100%;
  width: 100%;
}

.table-section {
  margin-bottom: 20px;
}

:deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

:deep(.el-statistic__title) {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  background: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table th) {
  background: #f5f7fa;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .statistics-container {
    padding: 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style>
