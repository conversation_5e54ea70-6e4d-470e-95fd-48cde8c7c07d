<template>
  <el-dropdown @command="handleLanguageChange" trigger="click">
    <el-button class="language-switcher">
      <span class="globe-icon">🌐</span>
      <span class="current-lang">{{ currentLanguageName }}</span>
      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          v-for="locale in supportedLocales" 
          :key="locale.code"
          :command="locale.code"
          :class="{ 'is-active': locale.code === currentLocale }"
        >
          <span class="locale-name">{{ locale.nativeName }}</span>
          <span class="locale-code">{{ locale.name }}</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ArrowDown } from '@element-plus/icons-vue'
import { supportedLocales, setLocale } from '@/locales'
import { getElementPlusLocale } from '@/utils/elementLocale'

const { locale } = useI18n()

// 当前语言
const currentLocale = computed(() => locale.value)

// 当前语言显示名称
const currentLanguageName = computed(() => {
  const current = supportedLocales.find(l => l.code === currentLocale.value)
  return current?.nativeName || 'English'
})

// 切换语言
const handleLanguageChange = (localeCode: string) => {
  setLocale(localeCode)
  
  // 更新Element Plus语言
  const elementLocale = getElementPlusLocale(localeCode)
  // 这里需要重新配置Element Plus，通常在应用级别处理
  window.location.reload() // 简单的重载页面来应用新语言
}
</script>

<style scoped>
.language-switcher {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  color: #333;
  font-weight: 500;
}

.language-switcher:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.language-switcher:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.globe-icon {
  font-size: 16px;
  margin-right: 4px;
}

.current-lang {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 下拉菜单样式优化 */

.locale-name {
  font-weight: 500;
  margin-right: 8px;
  color: #333;
}

.locale-code {
  font-size: 12px;
  color: #666;
}

.is-active {
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #409eff !important;
}

.is-active .locale-name {
  color: #409eff !important;
}

.is-active .locale-code {
  color: #409eff !important;
}

/* RTL支持 */
[dir="rtl"] .language-switcher {
  flex-direction: row-reverse;
}

[dir="rtl"] .globe-icon {
  margin-left: 4px;
  margin-right: 0;
}

[dir="rtl"] .locale-name {
  margin-left: 8px;
  margin-right: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-switcher {
    padding: 6px 10px;
    font-size: 13px;
  }

  .current-lang {
    font-size: 13px;
  }

  .locale-name {
    font-size: 14px;
  }

  .locale-code {
    font-size: 11px;
  }
}
</style>
