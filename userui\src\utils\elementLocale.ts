import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'
import ar from 'element-plus/es/locale/lang/ar'
import es from 'element-plus/es/locale/lang/es'
import fr from 'element-plus/es/locale/lang/fr'

// Element Plus语言映射
const elementLocaleMap: Record<string, any> = {
  'zh-CN': zhCn,
  'en-US': en,
  'ar-SA': ar,
  'es-ES': es,
  'fr-FR': fr
}

// 获取Element Plus对应的语言包
export function getElementPlusLocale(locale: string) {
  return elementLocaleMap[locale] || en
}
