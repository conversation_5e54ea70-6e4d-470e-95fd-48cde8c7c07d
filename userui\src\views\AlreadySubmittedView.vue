<template>
  <div class="submitted-container">
    <div class="page-container">
      <!-- 语言切换器 -->
      <div class="language-switcher-container">
        <LanguageSwitcher />
      </div>
      
      <div class="submitted-content">
        <div class="submitted-card">
          <div class="submitted-icon">
            <el-icon :size="80" color="#67c23a">
              <SuccessFilled />
            </el-icon>
          </div>

          <h1 class="submitted-title">{{ $t('alreadySubmitted.title') }}</h1>

          <p class="submitted-message">
            {{ $t('alreadySubmitted.message') }}
          </p>

          <div class="submitted-info">
            <el-icon :size="24" color="#409eff">
              <InfoFilled />
            </el-icon>
            <span>{{ $t('alreadySubmitted.info') }}</span>
          </div>

          <div class="submitted-actions">
            <el-button type="primary" @click="handleLogout">
              {{ $t('common.logout') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { SuccessFilled, InfoFilled } from '@element-plus/icons-vue'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const router = useRouter()
const userStore = useUserStore()
const { t } = useI18n()

// 退出登录
const handleLogout = () => {
  userStore.logout()
  ElMessage.success(t('common.logoutSuccess'))
  router.push('/login')
}
</script>

<style scoped>
.submitted-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.language-switcher-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

/* RTL支持 */
[dir="rtl"] .language-switcher-container {
  right: auto;
  left: 20px;
}

.submitted-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 100vh;
}

.submitted-card {
  width: 100%;
  max-width: 500px;
  padding: 60px 40px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  margin: 20px;
}

.submitted-icon {
  margin-bottom: 30px;
}

.submitted-title {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.2;
}

.submitted-message {
  font-size: 18px;
  color: #64748b;
  margin-bottom: 30px;
  line-height: 1.6;
}

.submitted-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: #f0f9ff;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 40px;
  color: #1e40af;
  font-size: 16px;
}

.submitted-actions {
  display: flex;
  justify-content: center;
}

.submitted-actions .el-button {
  padding: 12px 30px;
  font-size: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .submitted-card {
    padding: 40px 30px;
    border-radius: 12px;
    margin: 15px;
  }
  
  .submitted-title {
    font-size: 28px;
  }
  
  .submitted-message {
    font-size: 16px;
  }
  
  .submitted-info {
    padding: 14px 16px;
    font-size: 14px;
  }
}

/* RTL支持 */
[dir="rtl"] .submitted-card {
  text-align: right;
}

[dir="rtl"] .submitted-info {
  direction: rtl;
}
</style>
