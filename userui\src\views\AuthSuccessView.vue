<template>
  <div class="auth-success-container">
    <div class="auth-success-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-icon class="loading-icon" :size="40">
          <Loading />
        </el-icon>
        <h2>{{ $t('auth.processing') }}</h2>
        <p>{{ $t('auth.processingDesc') }}</p>
      </div>

      <!-- 成功状态 -->
      <div v-else-if="success" class="success-section">
        <el-icon class="success-icon" :size="60" color="#67C23A">
          <SuccessFilled />
        </el-icon>
        <h2>{{ $t('auth.loginSuccess') }}</h2>
        <p>{{ $t('auth.welcomeBack', { name: user?.name }) }}</p>
        <el-button type="primary" @click="redirectToApp" :loading="redirecting">
          {{ $t('auth.continueToApp') }}
        </el-button>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-section">
        <el-icon class="error-icon" :size="60" color="#F56C6C">
          <CircleCloseFilled />
        </el-icon>
        <h2>{{ $t('auth.loginFailed') }}</h2>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <el-button @click="retryLogin">{{ $t('auth.retry') }}</el-button>
          <el-button type="primary" @click="goHome">{{ $t('auth.backToHome') }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { authService, type AuthUser } from '@/services/authService'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const loading = ref(true)
const success = ref(false)
const errorMessage = ref('')
const user = ref<AuthUser | null>(null)
const redirecting = ref(false)

// 处理OAuth回调
const handleCallback = async () => {
  try {
    loading.value = true
    
    // 处理OAuth回调
    const callbackResult = authService.handleOAuthCallback()
    
    if (!callbackResult.success) {
      throw new Error(callbackResult.error || '认证失败')
    }
    
    // 检查认证状态
    const authUser = await authService.checkAuthStatus(callbackResult.token)
    
    if (!authUser) {
      throw new Error('无法获取用户信息')
    }
    
    // 保存用户信息到store
    userStore.setUser(authUser)
    
    user.value = authUser
    success.value = true
    
    // 显示成功消息
    ElMessage.success(t('auth.loginSuccessMessage'))
    
    // 自动跳转
    setTimeout(() => {
      redirectToApp()
    }, 2000)
    
  } catch (error) {
    console.error('OAuth回调处理失败:', error)
    success.value = false
    errorMessage.value = error instanceof Error ? error.message : '未知错误'
    ElMessage.error(t('auth.loginFailedMessage'))
  } finally {
    loading.value = false
  }
}

// 跳转到应用
const redirectToApp = async () => {
  try {
    redirecting.value = true
    
    // 根据用户状态决定跳转目标
    if (user.value?.hasSubmitted) {
      // 已提交过，跳转到结果页面
      await router.push('/result')
    } else {
      // 未提交，跳转到产品页面
      await router.push('/products')
    }
  } catch (error) {
    console.error('跳转失败:', error)
    ElMessage.error('跳转失败，请手动导航')
  } finally {
    redirecting.value = false
  }
}

// 重试登录
const retryLogin = () => {
  router.push('/')
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 组件挂载时处理回调
onMounted(() => {
  handleCallback()
})
</script>

<style scoped>
.auth-success-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-success-content {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.loading-section,
.success-section,
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.success-icon,
.error-icon {
  margin-bottom: 8px;
}

h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

p {
  margin: 0;
  color: #606266;
  font-size: 16px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

@media (max-width: 768px) {
  .auth-success-content {
    padding: 30px 20px;
  }
  
  .error-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .error-actions .el-button {
    width: 100%;
  }
}
</style>
