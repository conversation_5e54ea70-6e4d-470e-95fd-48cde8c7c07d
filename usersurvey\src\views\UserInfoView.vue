<template>
  <div class="user-info-container">
    <div class="header">
      <h1>补充资料</h1>
      <p class="subtitle">请填写您的基本信息，这将帮助我们更好地了解您的需求</p>
    </div>

    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" size="large">
        <el-form-item label="姓名" prop="UserName">
          <el-input v-model="form.UserName" placeholder="请输入您的姓名" />
        </el-form-item>

        <el-form-item label="性别" prop="UserSex">
          <el-radio-group v-model="form.UserSex">
            <el-radio value="男">男</el-radio>
            <el-radio value="女">女</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="生日" prop="Birthday">
          <el-date-picker
            v-model="form.Birthday"
            type="date"
            placeholder="请选择您的生日"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="国籍" prop="Country">
          <el-input v-model="form.Country" placeholder="请输入您的国籍" />
        </el-form-item>

        <el-form-item label="地址" prop="Address">
          <el-input 
            v-model="form.Address" 
            type="textarea" 
            :rows="3"
            placeholder="请输入您的详细地址"
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="actions">
      <el-button size="large" @click="skip">跳过</el-button>
      <el-button type="primary" size="large" @click="submit" :loading="submitting">
        提交
      </el-button>
    </div>

    <!-- 选择的产品摘要 -->
    <div class="selected-summary" v-if="selectedProductsCount > 0">
      <h3>您选择的产品</h3>
      <p>共选择了 {{ selectedProductsCount }} 个产品</p>
      <div class="product-groups">
        <div v-for="(products, groupIndex) in selectedProductsByGroup" :key="groupIndex" class="group-summary">
          <span class="group-label">分组 {{ groupIndex }}:</span>
          <span class="product-count">{{ products.length }} 个产品</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { useSurveyStore } from '@/stores/survey'
import axios from 'axios'

const router = useRouter()
const surveyStore = useSurveyStore()
const formRef = ref<InstanceType<typeof ElForm>>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  UserName: '',
  UserSex: '',
  Country: '',
  Birthday: '',
  Address: ''
})

// 表单验证规则
const rules = {
  UserName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  UserSex: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
}

// 计算属性
const selectedProductsByGroup = computed(() => {
  return surveyStore.getSelectedProductsByGroup()
})

const selectedProductsCount = computed(() => {
  return surveyStore.selectedProducts.length
})

// 跳过填写
const skip = async () => {
  try {
    console.log('跳过填写，当前路由:', router.currentRoute.value.path)
    console.log('当前选择的产品:', surveyStore.selectedProducts)

    // 检查是否有选择的产品
    if (surveyStore.selectedProducts.length === 0) {
      ElMessage.warning('请先选择产品再跳过')
      return
    }

    // 准备跳过请求数据
    const skipData = {
      selectedProductIds: surveyStore.selectedProducts.map(p => p.id)
    }

    console.log('跳过请求数据:', skipData)

    // 调用跳过 API，只保存投票数据
    const response = await axios.post('/api/User/SkipUserInfo', skipData)
    console.log('跳过 API 响应:', response.data)

    if (response.data?.success) {
      ElMessage.success('投票数据已保存，感谢您的参与！')

      // 清空调研数据
      surveyStore.reset()

      // 跳转到结束页面
      console.log('开始路由跳转...')
      try {
        const result = await router.replace('/end')
        console.log('路由跳转结果:', result)
        console.log('跳转后当前路由:', router.currentRoute.value.path)
      } catch (routeError) {
        console.error('路由跳转异常:', routeError)
        // 尝试使用 window.location 作为备选方案
        window.location.href = '/end'
      }
    } else {
      console.error('跳过失败:', response.data)
      ElMessage.error(response.data?.message || '跳过失败，请重试')
    }
  } catch (error) {
    console.error('跳过失败:', error)
    ElMessage.error('跳过失败，请重试')
  }
}

// 提交表单
const submit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) {
      console.log('表单验证失败')
      return
    }

    submitting.value = true
    console.log('开始提交表单')

    // 准备提交数据 - 转换字段名以匹配后端API
    const submitData = {
      userName: form.UserName,
      userSex: form.UserSex,
      country: form.Country,
      birthday: form.Birthday,
      address: form.Address,
      selectedProductIds: surveyStore.selectedProducts.map(p => p.id)
    }

    console.log('提交数据:', submitData)

    // 提交用户信息和选择的产品
    const response = await axios.post('/api/User/SubmitUserInfo', submitData)
    console.log('API响应:', response.data)

    if (response.data?.success) {
      ElMessage.success('提交成功！感谢您的帮助，我们会给您寄点小礼物以表感谢！')

      // 清空调研数据
      surveyStore.reset()

      // 跳转到结束页面
      console.log('准备跳转到结束页面')
      console.log('提交成功，当前路由:', router.currentRoute.value.path)
      try {
        const result = await router.replace('/end')
        console.log('路由跳转结果:', result)
        console.log('跳转后当前路由:', router.currentRoute.value.path)
      } catch (routeError) {
        console.error('路由跳转异常:', routeError)
        // 尝试使用 window.location 作为备选方案
        window.location.href = '/end'
      }
    } else {
      console.error('提交失败:', response.data)
      ElMessage.error(response.data?.message || '提交失败，请重试')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 组件挂载时的调试信息
onMounted(() => {
  console.log('UserInfoView 组件已挂载')
  console.log('当前路由:', router.currentRoute.value.path)
  console.log('选择的产品数量:', surveyStore.selectedProducts.length)
  console.log('选择的产品:', surveyStore.selectedProducts)
})
</script>

<style scoped>
.user-info-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 40px 20px;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #f5f5f5;
}

.header {
  text-align: left;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 2rem;
  font-weight: 400;
  margin-bottom: 15px;
  color: #409eff;
  letter-spacing: 0.5px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin: 0;
  font-weight: 400;
  line-height: 1.6;
}

.form-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin-bottom: 30px;
  transition: none;
}

.form-container:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  font-weight: 400;
  color: #333;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
  box-shadow: none;
  transition: all 0.2s ease;
  border: 1px solid #dcdfe6;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: none;
  border-color: #c0c4cc;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: none;
  border-color: #409eff;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
  box-shadow: none;
  transition: all 0.2s ease;
  border: 1px solid #dcdfe6;
}

:deep(.el-textarea__inner:hover) {
  box-shadow: none;
  border-color: #c0c4cc;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: none;
  border-color: #409eff;
}

:deep(.el-radio) {
  margin-right: 20px;
  font-weight: 400;
}

:deep(.el-date-editor) {
  border-radius: 4px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
}

.actions .el-button {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 400;
  border-radius: 4px;
  min-width: 100px;
  transition: all 0.2s ease;
}

:deep(.el-button--primary) {
  background: #409eff;
  border: 1px solid #409eff;
}

:deep(.el-button--primary:hover) {
  background: #66b1ff;
  border: 1px solid #66b1ff;
  transform: none;
  box-shadow: none;
}

:deep(.el-button:not(.el-button--primary)) {
  background: white;
  border: 1px solid #dcdfe6;
}

:deep(.el-button:not(.el-button--primary):hover) {
  background: #f5f7fa;
  transform: none;
  box-shadow: none;
  border-color: #c0c4cc;
}

.selected-summary {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-summary h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-weight: 400;
  font-size: 16px;
}

.product-groups {
  margin-top: 15px;
}

.group-summary {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.group-summary:last-child {
  border-bottom: none;
}

.group-label {
  color: #666;
  font-weight: 400;
}

.product-count {
  color: #409eff;
  font-weight: 400;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-info-container {
    padding: 30px 20px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 15px;
  }

  .form-container {
    padding: 30px 25px;
  }

  .actions {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .user-info-container {
    padding: 20px 15px;
  }

  .header h1 {
    font-size: 1.75rem;
  }

  .form-container {
    padding: 25px 20px;
  }

  :deep(.el-form-item__label) {
    font-size: 15px;
  }
}
</style>
