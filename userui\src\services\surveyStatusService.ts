import axios from 'axios'

// API基础URL - 自动适配协议
const getApiBaseUrl = () => {
  if (import.meta.env.DEV) {
    return '/api' // 开发环境使用代理
  }

  // 生产环境：自动适配当前页面协议
  const currentProtocol = window.location.protocol.replace(':', '')
  const host = import.meta.env.VITE_API_HOST || 'localhost'

  // 根据当前页面协议选择对应的后端端口
  const port = currentProtocol === 'https' ? 8081 : 8080
  const baseUrl = `${currentProtocol}://${host}:${port}/api`

  console.log(`🚀 SurveyStatusService API配置: ${baseUrl}`)
  return baseUrl
}

const API_BASE_URL = getApiBaseUrl()

export interface SurveyStatusResponse {
  success: boolean
  message: string
  data: {
    isOpen: boolean
  }
}

/**
 * 调查状态服务 - 专注于状态检查功能
 */
export class SurveyStatusService {
  private static isChecking = false
  private static lastCheckTime = 0
  private static cachedStatus: boolean | null = null
  private static readonly CACHE_DURATION = 5000 // 5秒缓存，确保状态变化能及时响应
  private static readonly MIN_CHECK_INTERVAL = 2000 // 最小检查间隔2秒，防止频繁调用
  private static checkCount = 0 // 检查次数计数器

  /**
   * 检查调查是否开放
   * @returns Promise<boolean> 调查是否开放
   */
  static async checkSurveyStatus(): Promise<boolean> {
    const now = Date.now()

    // 如果正在检查中，等待检查完成
    if (this.isChecking) {
      console.log('状态检查进行中，等待结果...')
      // 等待检查完成
      let attempts = 0
      while (this.isChecking && attempts < 30) { // 最多等待3秒
        await new Promise(resolve => setTimeout(resolve, 100))
        attempts++
      }
      return this.cachedStatus ?? true
    }

    // 防抖：如果距离上次检查时间太短，直接返回缓存结果
    if (this.lastCheckTime > 0 && (now - this.lastCheckTime) < this.MIN_CHECK_INTERVAL) {
      console.log('检查间隔太短，使用缓存结果:', this.cachedStatus)
      return this.cachedStatus ?? true
    }

    // 如果有缓存且未过期，直接返回缓存结果
    if (this.cachedStatus !== null && (now - this.lastCheckTime) < this.CACHE_DURATION) {
      console.log('使用缓存的状态结果:', this.cachedStatus)
      return this.cachedStatus
    }

    this.isChecking = true
    this.checkCount++
    console.log(`开始检查调查状态... (第${this.checkCount}次)`)

    try {
      const response = await axios.get<SurveyStatusResponse>(
        `${API_BASE_URL}/SurveyStatus/CheckStatus`,
        {
          timeout: 5000, // 5秒超时
        }
      )

      console.log('调查状态API响应:', response.data)

      if (response.data && response.data.success) {
        this.cachedStatus = response.data.data.isOpen
        this.lastCheckTime = now
        console.log('调查状态检查结果:', this.cachedStatus)
        return this.cachedStatus
      }

      // 如果响应格式不正确，抛出错误而不是默认返回true
      console.error('调查状态检查响应格式异常:', response.data)
      throw new Error('响应格式异常')
    } catch (error) {
      console.error('检查调查状态失败:', error)

      // 如果是网络错误且有缓存，使用缓存
      if (this.cachedStatus !== null) {
        console.log('网络错误，使用缓存状态:', this.cachedStatus)
        return this.cachedStatus
      }

      // 如果是首次检查失败，默认返回true（开放状态）
      console.log('首次检查失败，默认返回开放状态')
      this.cachedStatus = true
      this.lastCheckTime = now
      return true
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 清除缓存，强制下次检查时重新请求
   */
  static clearCache(): void {
    this.cachedStatus = null
    this.lastCheckTime = 0
    console.log('状态缓存已清除')
  }

  /**
   * 强制检查调查状态（忽略缓存但遵循防抖）
   * @returns Promise<boolean> 调查是否开放
   */
  static async forceCheckSurveyStatus(): Promise<boolean> {
    const now = Date.now()

    // 即使是强制检查，也要遵循最小间隔，防止疯狂调用
    if (this.lastCheckTime > 0 && (now - this.lastCheckTime) < this.MIN_CHECK_INTERVAL) {
      console.log('强制检查间隔太短，使用缓存结果:', this.cachedStatus)
      return this.cachedStatus ?? true
    }

    console.log('强制检查调查状态（忽略缓存）...')
    this.clearCache()
    return await this.checkSurveyStatus()
  }
}

export default SurveyStatusService
