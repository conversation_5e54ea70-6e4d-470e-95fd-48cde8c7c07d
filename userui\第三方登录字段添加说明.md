# 第三方登录字段添加功能实现说明

## 需求描述
在用户第三方登录后，无论用户选择"跳过"还是"提交"用户信息，都需要在用户表中保存第三方登录的相关信息：
- `ThirdPartyId`: 第三方平台用户ID
- `Provider`: 登录提供商（如 Google、Facebook）

## 实现方案

### 1. 数据库模型修改
用户模型 `Users.cs` 已经包含了所需的字段：
```csharp
[MaxLength(100)]
public string? ThirdPartyId { get; set; }

[MaxLength(50)]
public string? Provider { get; set; }
```

### 2. 后端API修改

#### 2.1 请求模型更新
在 `UserController.cs` 中更新了请求模型：

**UserSubmitRequest** (用户信息提交请求):
```csharp
public class UserSubmitRequest
{
    public string UserName { get; set; }
    public string UserSex { get; set; }
    public string Country { get; set; }
    public DateTime? Birthday { get; set; }
    public string Address { get; set; }
    public List<int> SelectedProductIds { get; set; } = new List<int>();
    public string ThirdPartyId { get; set; }  // 新增
    public string Provider { get; set; }      // 新增
}
```

**SkipUserInfoRequest** (跳过用户信息请求):
```csharp
public class SkipUserInfoRequest
{
    public List<int> SelectedProductIds { get; set; } = new List<int>();
    public string ThirdPartyId { get; set; }  // 新增
    public string Provider { get; set; }      // 新增
}
```

#### 2.2 业务逻辑层修改
在 `UserBLL.cs` 中更新了方法签名和实现：

**SubmitUserInfo 方法**:
```csharp
public (bool Success, string Message, int UserId) SubmitUserInfo(
    string userName, string userSex, string country, DateTime? birthday,
    string address, List<int> selectedProductIds, string thirdPartyId, string provider)
{
    // 创建用户记录时添加第三方信息
    var user = new Users
    {
        UserName = userName ?? "",
        UserSex = userSex ?? "",
        Country = country ?? "",
        Birthday = birthday,
        Address = address ?? "",
        RegisterTime = DateTime.Now,
        IsCompleted = !string.IsNullOrEmpty(userName),
        ThirdPartyId = thirdPartyId,  // 新增
        Provider = provider           // 新增
    };
}
```

**SkipUserInfo 方法**:
```csharp
public (bool Success, string Message, int UserId) SkipUserInfo(
    List<int> selectedProductIds, string thirdPartyId, string provider)
{
    // 创建匿名用户记录时添加第三方信息
    var anonymousUser = new Users
    {
        UserName = "匿名用户",
        UserSex = "",
        Country = "",
        Birthday = null,
        Address = "",
        RegisterTime = DateTime.Now,
        IsCompleted = false,
        ThirdPartyId = thirdPartyId,  // 新增
        Provider = provider           // 新增
    };
}
```

### 3. 前端修改

#### 3.1 导入用户状态管理
在 `UserInfoView.vue` 中添加了用户状态管理的导入：
```typescript
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
```

#### 3.2 跳过功能修改
在跳过请求中添加第三方登录信息：
```typescript
const skipData = {
  selectedProductIds: surveyStore.selectedProducts.map(p => p.id),
  thirdPartyId: userStore.currentUser?.providerId || '',
  provider: userStore.currentUser?.provider || ''
}
```

#### 3.3 提交功能修改
在提交请求中添加第三方登录信息：
```typescript
const submitData = {
  userName: form.UserName,
  userSex: form.UserSex,
  country: form.Country,
  birthday: form.Birthday,
  address: form.Address,
  selectedProductIds: surveyStore.selectedProducts.map(p => p.id),
  thirdPartyId: userStore.currentUser?.providerId || '',
  provider: userStore.currentUser?.provider || ''
}
```

## 数据流程

1. **用户第三方登录**: 用户通过 Google 或 Facebook 登录，用户信息保存在前端 `userStore` 中
2. **产品选择**: 用户选择感兴趣的产品
3. **用户信息页面**: 用户可以选择填写详细信息或跳过
4. **数据提交**: 无论选择提交还是跳过，都会将第三方登录信息一并发送到后端
5. **数据保存**: 后端将用户信息（包括第三方登录信息）保存到数据库

## 关键特性

- **向后兼容**: 新增字段为可选字段，不影响现有功能
- **数据完整性**: 确保第三方登录用户的身份信息得到保存
- **统一处理**: 提交和跳过操作都统一处理第三方登录信息
- **安全性**: 第三方ID和提供商信息安全传输和存储

## 测试建议

1. 测试第三方登录后的用户信息提交
2. 测试第三方登录后的跳过操作
3. 验证数据库中第三方字段的正确保存
4. 测试非第三方登录用户的兼容性（字段为空值）

## 注意事项

- 确保前端 `userStore` 中的用户信息格式正确
- 第三方字段允许为空，以兼容非第三方登录的情况
- 建议在生产环境部署前进行充分测试
