/**
 * API配置文件
 * 统一管理所有API相关的配置
 */

import { getEnvironmentConfig } from '@/utils/environment'

// 获取环境配置
const envConfig = getEnvironmentConfig()

// API基础配置
export const apiConfig = {
  // 基础配置
  baseURL: envConfig.apiBaseUrl,
  host: envConfig.apiHost,
  port: envConfig.apiPort,
  protocol: envConfig.apiProtocol,
  timeout: envConfig.apiTimeout,

  // 环境信息
  env: envConfig.env,
  isDev: envConfig.isDev,
  isProd: envConfig.isProd,
  isHttps: envConfig.isHttps,
  forceHttps: envConfig.forceHttps,

  // 调试配置
  debugMode: envConfig.debugMode,
  logLevel: envConfig.logLevel,
  showConsoleLogs: envConfig.showConsoleLogs,
  
  // API端点配置
  endpoints: {
    // 基础端点
    auth: import.meta.env.VITE_API_AUTH_ENDPOINT || '/api/auth',
    user: import.meta.env.VITE_API_USER_ENDPOINT || '/api/user',
    product: import.meta.env.VITE_API_PRODUCT_ENDPOINT || '/api/product',
    admin: import.meta.env.VITE_API_ADMIN_ENDPOINT || '/api/admin',
    upload: import.meta.env.VITE_API_UPLOAD_ENDPOINT || '/api/upload',

    // 详细端点
    auth_routes: {
      google: (import.meta.env.VITE_API_AUTH_ENDPOINT || '/api/auth') + '/google',
      facebook: (import.meta.env.VITE_API_AUTH_ENDPOINT || '/api/auth') + '/facebook',
      checkSubmission: (import.meta.env.VITE_API_AUTH_ENDPOINT || '/api/auth') + '/check-submission'
    },

    user_routes: {
      profile: (import.meta.env.VITE_API_USER_ENDPOINT || '/api/user') + '/profile',
      checkStatus: (import.meta.env.VITE_API_USER_ENDPOINT || '/api/user') + '/CheckSubmissionStatus',
      update: (import.meta.env.VITE_API_USER_ENDPOINT || '/api/user') + '/update'
    },

    product_routes: {
      list: (import.meta.env.VITE_API_PRODUCT_ENDPOINT || '/api/product'),
      detail: (import.meta.env.VITE_API_PRODUCT_ENDPOINT || '/api/product'),
      upload: (import.meta.env.VITE_API_PRODUCT_ENDPOINT || '/api/product') + '/upload'
    },

    admin_routes: {
      users: (import.meta.env.VITE_API_ADMIN_ENDPOINT || '/api/admin') + '/users',
      statistics: (import.meta.env.VITE_API_ADMIN_ENDPOINT || '/api/admin') + '/statistics',
      products: (import.meta.env.VITE_API_ADMIN_ENDPOINT || '/api/admin') + '/products'
    },

    upload_routes: {
      image: (import.meta.env.VITE_API_UPLOAD_ENDPOINT || '/api/upload') + '/image',
      file: (import.meta.env.VITE_API_UPLOAD_ENDPOINT || '/api/upload') + '/file'
    }
  }
}

// 获取完整的API URL
export const getApiUrl = (endpoint: string): string => {
  // 如果是开发模式且配置了代理，使用相对路径
  if (apiConfig.isDev && typeof window !== 'undefined') {
    return endpoint
  }
  
  // 否则使用完整URL
  return apiConfig.baseURL + endpoint
}

// 获取特定端点的URL
export const getEndpointUrl = (category: keyof typeof apiConfig.endpoints, endpoint: string): string => {
  const endpointPath = (apiConfig.endpoints[category] as any)[endpoint]
  if (!endpointPath) {
    throw new Error('未找到端点: ' + category + '.' + endpoint)
  }
  return getApiUrl(endpointPath)
}

// API请求配置
export const getRequestConfig = (options: RequestInit = {}): RequestInit => {
  return {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }
}

// 验证API配置
export const validateApiConfig = () => {
  const errors: string[] = []
  const warnings: string[] = []

  if (!apiConfig.baseURL) {
    errors.push('API基础URL未配置')
  }

  if (apiConfig.timeout < 1000) {
    warnings.push('API超时时间可能过短')
  }

  if (apiConfig.isDev && !apiConfig.baseURL.includes('localhost')) {
    warnings.push('开发模式下建议使用localhost')
  }

  return {
    isValid: errors.length === 0,
    hasWarnings: warnings.length > 0,
    errors,
    warnings
  }
}

// 导出常用的API URL
export const API_URLS = {
  // 认证
  GOOGLE_LOGIN: getEndpointUrl('auth_routes', 'google'),
  FACEBOOK_LOGIN: getEndpointUrl('auth_routes', 'facebook'),
  CHECK_SUBMISSION: getEndpointUrl('auth_routes', 'checkSubmission'),

  // 用户
  USER_PROFILE: getEndpointUrl('user_routes', 'profile'),
  USER_CHECK_STATUS: getEndpointUrl('user_routes', 'checkStatus'),

  // 产品
  PRODUCT_LIST: getEndpointUrl('product_routes', 'list'),
  PRODUCT_UPLOAD: getEndpointUrl('product_routes', 'upload'),

  // 上传
  UPLOAD_IMAGE: getEndpointUrl('upload_routes', 'image')
}
