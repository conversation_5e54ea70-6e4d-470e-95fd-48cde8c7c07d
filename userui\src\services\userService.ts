import axios from 'axios'

export interface SubmissionCheckResponse {
  success: boolean
  hasSubmitted: boolean
  message?: string
}

export interface UserSubmissionData {
  userName: string
  userSex: string
  country: string
  birthday: string
  address: string
  selectedProductIds: number[]
  thirdPartyId: string
  provider: string
}

export interface SkipSubmissionData {
  selectedProductIds: number[]
  thirdPartyId: string
  provider: string
}

class UserService {
  /**
   * 检查用户是否已提交过调研
   */
  async checkSubmissionStatus(thirdPartyId: string, provider: string): Promise<SubmissionCheckResponse> {
    try {
      const response = await axios.get('/api/User/CheckSubmissionStatus', {
        params: {
          thirdPartyId,
          provider
        }
      })

      return {
        success: response.data?.success || false,
        hasSubmitted: response.data?.hasSubmitted || false,
        message: response.data?.message
      }
    } catch (error) {
      console.error('检查提交状态失败:', error)
      return {
        success: false,
        hasSubmitted: false,
        message: '检查提交状态失败'
      }
    }
  }

  /**
   * 提交用户信息和产品选择
   */
  async submitUserInfo(data: UserSubmissionData) {
    try {
      const response = await axios.post('/api/User/SubmitUserInfo', data)
      return response.data
    } catch (error) {
      console.error('提交用户信息失败:', error)
      throw error
    }
  }

  /**
   * 跳过用户信息填写，只提交产品选择
   */
  async skipUserInfo(data: SkipSubmissionData) {
    try {
      const response = await axios.post('/api/User/SkipUserInfo', data)
      return response.data
    } catch (error) {
      console.error('跳过用户信息失败:', error)
      throw error
    }
  }
}

export const userService = new UserService()
