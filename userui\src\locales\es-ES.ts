export default {
  // Común
  common: {
    submit: 'Enviar',
    skip: '<PERSON><PERSON><PERSON>',
    next: 'Siguiente Grupo',
    previous: 'Grupo Anterior',
    loading: 'Cargando...',
    logout: 'Cerrar Sesi<PERSON>',
    cancel: 'Cancelar',
    confirm: 'Confirmar'
  },
  
  // Página de inicio de sesión
  login: {
    title: 'Sistema de Investigación de Usuarios',
    subtitle: 'Por favor selecciona el método de inicio de sesión',
    googleLogin: 'Iniciar sesión con Google',
    facebookLogin: 'Iniciar sesión con Facebook',
    loginSuccess: 'Inicio de sesión exitoso',
    loginFailed: 'Error al iniciar sesión, por favor inténtalo de nuevo',
    loggingIn: 'Iniciando sesión...',
    facebookNotConfigured: 'Facebook No Configurado',
    facebookConfigNotice: 'Inicio de Sesión con Facebook No Configurado',
    facebookConfigDesc: 'El inicio de sesión con Facebook requiere configuración de App ID para funcionar.',
    facebookConfigGuide: 'Por favor consulta FACEBOOK_SETUP_GUIDE.md para la configuración.',
    inAppBrowserWarning: 'Navegador dentro de la aplicación detectado, se recomienda copiar el enlace al navegador del sistema para la mejor experiencia de inicio de sesión',
    configError: 'Error de Configuración',
    facebookNotConfiguredError: 'Inicio de sesión con Facebook no configurado, por favor contacta al administrador para configurar Facebook App ID',
    inAppBrowserDetected: 'Navegador Dentro de la Aplicación Detectado',
    inAppBrowserDesc: 'Para un inicio de sesión exitoso, se recomienda abrir en el navegador del sistema',
    viewLoginGuide: 'Ver Guía de Inicio de Sesión',
    copyPageLink: 'Copiar Enlace de Página',
    pageLinkCopied: 'Enlace de página copiado al portapapeles',
    recommendSystemBrowser: 'Se recomienda iniciar sesión con el navegador del sistema para la mejor experiencia'
  },
  
  // Página de selección de productos
  products: {
    title: 'Selección de Productos',
    groupInfo: 'Grupo {current} / {total}',
    productCount: '({count} productos)',
    selectedCount: 'Seleccionados {selected} / {max} productos',
    selectionHint: 'Puedes seleccionar hasta {max} productos por grupo',
    noData: 'No hay datos de productos disponibles',
    selectAtLeastOne: 'Por favor selecciona al menos un producto en un grupo',
    selectProductFirst: 'Por favor selecciona productos antes de omitir',
    logoutSuccess: 'Sesión cerrada exitosamente',
    dislikeAll: 'No me gusta ninguno',
    dislikeAllTitle: 'Confirmar acción',
    dislikeAllConfirm: '¿Estás seguro de que no te gusta ningún producto?',
    tooManySelected: 'Puedes seleccionar como máximo {max} productos',
    noProductSelected: 'Actualmente no hay productos seleccionados'
  },
  
  // Página de información del usuario
  userInfo: {
    title: 'Completar Perfil',
    subtitle: 'Por favor completa tu información básica para ayudarnos a entender mejor tus necesidades',
    name: 'Nombre',
    namePlaceholder: 'Por favor ingresa tu nombre',
    nameRequired: 'Por favor ingresa tu nombre',
    gender: 'Género',
    genderRequired: 'Por favor selecciona el género',
    male: 'Masculino',
    female: 'Femenino',
    birthday: 'Cumpleaños',
    birthdayPlaceholder: 'Por favor selecciona tu cumpleaños',
    country: 'País',
    countryPlaceholder: 'Por favor ingresa tu país',
    address: 'Dirección',
    addressPlaceholder: 'Por favor ingresa tu dirección detallada',
    selectedProducts: 'Tus Productos Seleccionados',
    totalSelected: 'Total {count} productos seleccionados',
    groupLabel: 'Grupo {index}:',
    submitSuccess: '¡Enviado exitosamente! ¡Gracias por tu ayuda, te enviaremos un pequeño regalo como agradecimiento!',
    skipSuccess: 'Datos de votación guardados, ¡gracias por tu participación!',
    submitFailed: 'Error al enviar, por favor inténtalo de nuevo',
    skipFailed: 'Error al omitir, por favor inténtalo de nuevo'
  },
  
  // Página final
  end: {
    title: '¡Envío Exitoso!',
    message: '¡Gracias por participar en nuestra investigación de productos!<br>Tus valiosos comentarios nos ayudarán a proporcionar mejores productos y servicios.',
    giftInfo: '¡Te enviaremos un pequeño regalo como agradecimiento!'
  },
  
  // Página de servicio no disponible
  serviceUnavailable: {
    title: 'Sitio web temporalmente inaccesible',
    description: 'Lo sentimos, el sistema de investigación está temporalmente cerrado y no puede proporcionar servicio.',
    subDescription: 'El sistema detectará automáticamente el estado del servicio y redirigirá a la página de inicio de sesión una vez restaurado. Haz clic en el botón de actualizar para verificar el estado del sistema.',
    refresh: 'Actualizar página',
    contact: 'Si tienes alguna pregunta, por favor contacta al administrador del sistema',
    systemRestored: 'El sistema de investigación ha sido restaurado, redirigiendo...',
    systemStillClosed: 'El sistema de investigación aún está cerrado, por favor inténtalo más tarde',
    checkFailed: 'Error al verificar el estado, por favor inténtalo más tarde'
  },

  // Mensajes de error
  errors: {
    networkError: 'Error de red, por favor verifica tu conexión',
    serverError: 'Error del servidor, por favor inténtalo más tarde',
    unknownError: 'Error desconocido, por favor inténtalo de nuevo'
  },

  // Página de callback de autenticación
  auth: {
    processing: 'Procesando Información de Inicio de Sesión',
    processingDesc: 'Por favor espera, estamos verificando tu identidad...',
    loginSuccess: '¡Inicio de sesión exitoso! Redirigiendo...',
    loginFailed: 'Error al iniciar sesión, por favor inténtalo de nuevo',
    networkError: 'Error de conexión de red, por favor verifica la red e inténtalo de nuevo',
    serverError: 'Servidor temporalmente no disponible, por favor inténtalo más tarde',
    authError: 'Error de autorización, por favor inicia sesión de nuevo',
    noAuthCode: 'No se recibió código de autorización',
    securityError: 'Error de verificación de seguridad, por favor inicia sesión de nuevo',
    backendError: 'Error de respuesta del backend',
    userCancelled: 'Autorización cancelada, regresando a la página de inicio de sesión',
    noAuthCode: 'No se recibió código de autorización',
    securityError: 'Falló la verificación de seguridad, por favor inicie sesión nuevamente',
    loginSuccess: '¡Inicio de sesión exitoso! Redirigiendo...',
    loginFailed: 'Falló el inicio de sesión',
    networkError: 'Falló la conexión de red, por favor verifique la red e intente nuevamente',
    serverError: 'Servidor temporalmente no disponible, por favor intente más tarde',
    authError: 'Falló la autorización, por favor inicie sesión nuevamente'
  },

  // Página de guía de inicio de sesión móvil
  mobileGuide: {
    title: '📱 Guía de Inicio de Sesión Móvil',
    subtitle: 'Para obtener la mejor experiencia de inicio de sesión, siga estos pasos',
    environmentDetection: '🔍 Detección del Entorno Actual',
    deviceType: 'Tipo de Dispositivo:',
    mobileDevice: 'Dispositivo Móvil',
    desktopDevice: 'Dispositivo de Escritorio',
    browserEnvironment: 'Entorno del Navegador:',
    recommendedMethod: 'Método Recomendado:',
    systemBrowser: 'Navegador del Sistema',
    wechatBrowser: 'Navegador Integrado de WeChat',
    qqBrowser: 'Navegador Integrado de QQ',
    alipayBrowser: 'Navegador Integrado de Alipay',
    inAppBrowser: 'Navegador Dentro de la App',
    directLogin: 'Inicio de Sesión Directo',
    copyToSystemBrowser: 'Copiar enlace al navegador del sistema',
    mobileOptimizedLogin: 'Inicio de sesión optimizado para móvil',
    inAppBrowserGuide: '⚠️ Guía de Inicio de Sesión en Navegador Dentro de la App',
    inAppWarning: 'Se detectó que está usando un navegador dentro de la aplicación (como WeChat, QQ, etc.), lo que puede afectar la funcionalidad de inicio de sesión de terceros.',
    step1Title: 'Copiar enlace de la página actual',
    step1Desc: 'Haga clic en el botón de abajo para copiar el enlace de la página',
    copyLink: '📋 Copiar Enlace',
    step2Title: 'Abrir en navegador del sistema',
    step2Desc: 'Abra el navegador del sistema de su teléfono (Safari, Chrome, etc.), pegue el enlace y visite',
    step3Title: 'Completar inicio de sesión',
    step3Desc: 'Haga clic en el botón de inicio de sesión de Google en el navegador del sistema para completar la autenticación',
    mobileTips: '📱 Consejos de Inicio de Sesión Móvil',
    tip1: 'Asegúrese de tener una conexión de red estable',
    tip2: 'Permitir ventanas emergentes del navegador (si es necesario)',
    tip3: 'No cambie de aplicación durante el inicio de sesión',
    openedInSystemBrowser: 'He abierto en el navegador del sistema',
    startLogin: 'Iniciar Sesión',
    confirmed: '✅ Confirmado',
    confirmCopied: 'Confirmar enlace copiado',
    backToHome: 'Volver al Inicio',
    linkCopied: 'Enlace copiado al portapapeles',
    pleaseConfirmFirst: 'Por favor, copie el enlace y ábralo en el navegador del sistema primero'
  }
}
